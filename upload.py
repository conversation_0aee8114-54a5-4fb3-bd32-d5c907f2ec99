import json
import logging
import os
import random
import re
import shutil
import threading
import time
import uuid
from datetime import datetime, timedelta
from pathlib import Path

import cv2
import instaloader
import psutil
from PyQt5.QtCore import pyqtSlot
from apscheduler.schedulers.background import BackgroundScheduler
from instagrapi import Client
from instagrapi.exceptions import LoginRequired
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

from download import extract_shortcode_from_url, download_instagram_video_simple, download_twitter_media, download_ffmpeg, \
    download_all_profiles_links, normalize_url
from utils import dummy_duration, create_video_thumbnail, protect_media_files, safe_file_delete
from stats import stats_system, update_stats_display_html, update_can_bars_ui

# ===== DOSYA BAŞINA EKLE =====
_profile_locks: dict[str, threading.Lock] = {}
# ===== DOSYA BAŞINA EKLE =====

import base64
import requests
from PyQt5.QtCore import QObject, pyqtSignal, pyqtSlot


def clean_text_for_chrome(text):
    """
    Chrome driver için metni temizler - Unicode karakterleri ve emojileri kaldırır
    """
    if not text:
        return ""

    # Unicode karakterleri ASCII'ye dönüştür veya kaldır
    try:
        # Önce normalize et
        import unicodedata
        text = unicodedata.normalize('NFKD', text)

        # BMP dışındaki karakterleri kaldır (emojiler vs.)
        text = ''.join(char for char in text if ord(char) <= 0xFFFF)

        # ASCII olmayan karakterleri transliterate et veya kaldır
        text = text.encode('ascii', 'ignore').decode('ascii')

        # Fazla boşlukları temizle
        text = ' '.join(text.split())

        return text
    except Exception as e:
        logging.warning(f"Text cleaning failed: {e}")
        # Fallback: sadece ASCII karakterleri tut
        return ''.join(char for char in text if ord(char) < 128)


def create_new_driver_for_account(account_username):
    import undetected_chromedriver as uc
    import psutil
    import time

    # Kill any existing Chrome processes for this profile
    base_dir = os.path.dirname(os.path.abspath(__file__))
    unique_profile_path = os.path.join(base_dir, f"chrome_profile_{account_username}")

    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                if unique_profile_path in ' '.join(proc.info['cmdline'] or []):
                    logging.info(f"Killing existing Chrome process for {account_username}: {proc.info['pid']}")
                    proc.terminate()
                    proc.wait(timeout=5)
    except Exception as e:
        logging.warning(f"Chrome process cleanup failed for {account_username}: {e}")

    # Clean up profile directory if it exists and is problematic
    if os.path.exists(unique_profile_path):
        try:
            # Try to remove lock files that might cause issues
            lock_files = ['SingletonLock', 'SingletonSocket', 'SingletonCookie']
            for lock_file in lock_files:
                lock_path = os.path.join(unique_profile_path, lock_file)
                if os.path.exists(lock_path):
                    os.remove(lock_path)
                    logging.debug(f"Removed lock file: {lock_path}")
        except Exception as e:
            logging.warning(f"Lock file cleanup failed for {account_username}: {e}")

    os.makedirs(unique_profile_path, exist_ok=True)

    options = uc.ChromeOptions()
    options.add_argument(f'--user-data-dir={unique_profile_path}')
    options.add_argument('--headless=new')
    options.add_argument('--disable-gpu')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-notifications')
    options.add_argument('--disable-web-security')
    options.add_argument('--disable-features=VizDisplayCompositor')
    options.add_argument('--window-size=1920,1080')
    options.add_argument('--remote-debugging-port=0')  # Use random port
    options.add_argument('--lang=en-US')  # Set language to English
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) ' +
                         'AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36')

    # Add encoding preferences to handle Unicode better
    prefs = {
        "profile.default_content_setting_values.notifications": 2,
        "profile.default_content_settings.popups": 0,
        "profile.managed_default_content_settings.images": 2
    }
    options.add_experimental_option("prefs", prefs)

    # Try to create driver with retries
    max_retries = 3
    for attempt in range(max_retries):
        try:
            logging.info(f"Creating Chrome driver for {account_username} (attempt {attempt + 1}/{max_retries})")
            driver = uc.Chrome(options=options)
            logging.info(f"Chrome driver created successfully for {account_username}")
            return driver
        except Exception as e:
            logging.error(f"Chrome driver creation failed for {account_username} (attempt {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2)  # Wait before retry
            else:
                # Last attempt failed, try with minimal options
                try:
                    logging.info(f"Trying minimal Chrome options for {account_username}")
                    minimal_options = uc.ChromeOptions()
                    minimal_options.add_argument('--headless=new')
                    minimal_options.add_argument('--no-sandbox')
                    minimal_options.add_argument('--disable-dev-shm-usage')
                    minimal_options.add_argument('--remote-debugging-port=0')
                    driver = uc.Chrome(options=minimal_options)
                    logging.info(f"Chrome driver created with minimal options for {account_username}")
                    return driver
                except Exception as e2:
                    logging.error(f"All Chrome driver creation attempts failed for {account_username}: {e2}")
                    raise e2


def login_twitter_account(driver, username, password):
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    import time

    try:
        logging.info("Twitter'a giriş yapılıyor...")
        driver.get("https://x.com/i/flow/login")
        time.sleep(5)
        driver.execute_script("window.localStorage.clear();")
        driver.execute_script("window.sessionStorage.clear();")
        # Tüm çerezleri silmek için:
        driver.execute_script("""
            document.cookie.split(';').forEach(function(c) {
                document.cookie = c.trim().split('=')[0] + '=; expires=Thu, 01 Jan 1970 00:00:00 UTC;';
            });
        """)

        user_input = WebDriverWait(driver, 30).until(
            EC.presence_of_element_located((By.XPATH, '//input[@autocomplete="username"]'))
        )
        user_input.clear()
        user_input.send_keys(username)
        time.sleep(1)
        next_button = WebDriverWait(driver, 20).until(
            EC.element_to_be_clickable(
                (By.XPATH, "//span[text()='Next']/ancestor::button | //span[text()='İleri']/ancestor::button"))
        )
        next_button.click()
        time.sleep(5)

        password_input = None
        password_xpaths = [
            '//input[@autocomplete="current-password"]',
            '//input[@name="password"]',
            '//input[@type="password"]'
        ]
        for xpath in password_xpaths:
            try:
                password_input = WebDriverWait(driver, 20).until(
                    EC.presence_of_element_located((By.XPATH, xpath))
                )
                if password_input:
                    logging.info(f"Şifre alanı bulundu: {xpath}")
                    break
            except Exception:
                continue

        if not password_input:
            logging.error("Şifre giriş alanı bulunamadı.")
            return False

        password_input.clear()
        password_input.send_keys(password)
        time.sleep(1)
        login_button = WebDriverWait(driver, 20).until(
            EC.element_to_be_clickable((By.XPATH, "//button[@data-testid='LoginForm_Login_Button']"))
        )
        driver.execute_script("arguments[0].click();", login_button)
        time.sleep(10)
        logging.info(f"{username} ile giriş denemesi tamamlandı.")
        return True
    except Exception as e:
        logging.error(f"{username} ile giriş hatası: {str(e)}")
        return False


def schedule_all_profiles_uploads(scheduler):
    """
    Tüm platformlardaki .json dosyalarını okuyup
    her profilde tanımlı 'schedule' saatleri için APScheduler job'ları ekler.
    Saat geldiğinde 'upload_profile_videos(profile_path, platform)' fonksiyonunu tetikler.
    """
    base_dir = os.path.dirname(os.path.abspath(__file__))
    config_dir = os.path.join(base_dir, "configuration")

    # Hangi platformlara bakacağız?
    platforms = ["instagram", "twitter", "youtube"]

    # Gün isimlerini APScheduler cron formatına uyarlamak için bir sözlük:
    day_map = {
        "monday": "0",
        "tuesday": "1",
        "wednesday": "2",
        "thursday": "3",
        "friday": "4",
        "saturday": "5",
        "sunday": "6",
    }

    for platform in platforms:
        platform_dir = os.path.join(config_dir, platform)
        if not os.path.exists(platform_dir):
            continue

        # Tüm profil dosyalarını gez
        for profile_file in os.listdir(platform_dir):
            if not profile_file.endswith(".json"):
                continue
            profile_path = os.path.join(platform_dir, profile_file)
            try:
                with open(profile_path, "r", encoding="utf-8") as f:
                    profile_data = json.load(f)

                # schedule alanını okuyalım
                schedule_dict = profile_data.get("schedule", {})  # { "monday": ["10:00","14:30"], ... }

                # Her gün için tanımlanan saatleri APScheduler cron job olarak ekle
                for day_name, time_list in schedule_dict.items():
                    # Ör: "monday", ["10:00", "15:05"]
                    if day_name.lower() not in day_map:
                        continue  # Geçerli bir gün değilse atla
                    day_of_week_str = day_map[day_name.lower()]

                    for t_str in time_list:
                        # t_str = "HH:MM" formatında
                        try:
                            hour, minute = t_str.split(":")
                            hour = int(hour)
                            minute = int(minute)

                            # Job ekliyoruz: tam bu gün ve bu saatte -> upload_profile_videos(profile_path, platform)
                            scheduler.add_job(
                                upload_profile_videos,
                                'cron',
                                day_of_week=day_of_week_str,
                                hour=hour,
                                minute=minute,
                                args=[profile_path, platform],
                                id=f"{platform}_{profile_file}_{day_of_week_str}_{hour}_{minute}",
                                replace_existing=True,
                                misfire_grace_time=300,  # 5 dk gecikse de çalıştır
                                coalesce=True           # Arka arkaya kaçan tetiklemeleri birleştir
                            )
                            logging.info(f"Job eklendi: {platform}, {profile_file}, {day_name} {t_str}")
                        except ValueError as ve:
                            logging.error(f"Schedule parse error: {t_str} => {ve}")
            except Exception as e:
                logging.error(f"Profil dosyası okunurken hata: {profile_file} => {e}")


def upload_profile_videos(profile_path, platform):
    """
    Saat geldiğinde APScheduler tarafından çağrılır.
    Daha önce indirilen videoları 'upload_for_profile' fonksiyonları ile paylaşır.
    """
    base_dir = os.path.dirname(os.path.abspath(__file__))
    videos_dir = os.path.join(base_dir, "videos")

    if platform == "instagram":
        output_dir = os.path.join(videos_dir, "instagramdownloaded")
    elif platform == "twitter":
        output_dir = os.path.join(videos_dir, "twitterdownloaded")
    elif platform == "youtube":
        output_dir = os.path.join(videos_dir, "youtubedownloaded")
    else:
        logging.warning(f"Tanımsız platform: {platform}")
        return

    # Profil JSON'u aç
    try:
        with open(profile_path, "r", encoding="utf-8") as f:
            profile_data = json.load(f)
    except Exception as e:
        logging.error(f"upload_profile_videos: Profil dosyası açılamadı: {profile_path} => {e}")
        return

    # Şimdi 'upload_instagram_video_for_profile(...)' gibi fonksiyonları çağırabilirsiniz.
    if platform == "instagram":
        success = upload_instagram_video_for_profile(profile_path, profile_data, output_dir)
        if success:
            logging.info(f"Instagram profilinde upload tamam: {profile_path}")
    elif platform == "twitter":
        success = upload_twitter_video_for_profile(profile_path, profile_data, output_dir)
        if success:
            logging.info(f"Twitter profilinde upload tamam: {profile_path}")
    elif platform == "youtube":
        # Burada youtube upload mantığı varsa
        pass


def process_profile_links(platform, output_dir, temp_dir, ffmpeg_dir):
    """
    İlgili platformun configuration klasöründeki tüm profilleri,
    JSON dosyasındaki "links" listesini işleyerek, indirme ve yükleme işlemlerini gerçekleştirir.
    İndirme başarılıysa, link nesnesini { "url": ..., "description": ... } olarak günceller.
    Yükleme başarılı ise, ilgili linki JSON'dan kaldırır.

    Önemli değişiklik: Instagram linkleri için tek bir Instaloader nesnesi oluşturulup
    tüm indirme işlemleri için aynı oturum kullanılıyor.
    """
    base_dir = os.path.dirname(os.path.abspath(__file__))
    config_dir = os.path.join(base_dir, "configuration", platform)
    if not os.path.exists(config_dir):
        logging.info(f"{platform} konfigürasyon klasörü bulunamadı: {config_dir}")
        return

    # Instagram için geçerli bir kullanıcı adı ve şifre bul
    valid_username = ""
    valid_password = ""
    shared_loader = None

    if platform == "instagram":
        # Instagram için tek bir oturum oluştur
        instagram_config_dir = os.path.join(base_dir, "configuration", "instagram")
        profile_files = [f for f in os.listdir(instagram_config_dir) if f.endswith(".json")]

        # Önce geçerli bir kullanıcı adı ve şifre bul
        valid_username = ""
        valid_password = ""
        for pfile in profile_files:
            profile_path = os.path.join(instagram_config_dir, pfile)
            try:
                with open(profile_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                username = data.get("username", "").strip()
                password = data.get("password", "").strip()
                if username and password:
                    valid_username = username
                    valid_password = password
                    break
            except Exception as e:
                logging.warning(f"Profil dosyası okunurken hata ({pfile}): {e}")

        # Oturum dizinini oluştur
        session_dir = os.path.join(instagram_config_dir, "sessions")
        os.makedirs(session_dir, exist_ok=True)

        # get_instaloader fonksiyonu ile tek bir oturum oluştur ve kullan
        shared_loader = None
        if valid_username and valid_password:
            try:
                # No longer using authentication - simplified approach
                logging.info(f"Using simplified Instagram approach (no authentication required)")
            except Exception as e:
                logging.warning(f"Instagram setup warning: {e}")
                shared_loader = None

    profile_files = [f for f in os.listdir(config_dir) if f.endswith(".json")]
    for profile_file in profile_files:
        profile_path = os.path.join(config_dir, profile_file)

        # ----------- YENİ: tek seferde tek thread kilidi -----------
        profile_lock = _profile_locks.setdefault(profile_path, threading.Lock())
        with profile_lock:
        # -----------------------------------------------------------

            try:
                with open(profile_path, "r", encoding="utf-8") as f:
                    profile_data = json.load(f)

                # Önce indirilen URL'leri bir sete ekle (URL'leri normalize et)
                downloaded_urls = set()
                for item in profile_data.get("downloaded", []):
                    if isinstance(item, dict) and "url" in item:
                        normalized_url = normalize_url(item["url"])  # URL'yi normalize et
                        downloaded_urls.add(normalized_url)

                links = profile_data.get("links", [])
                if not links:
                    continue

                updated = False  # JSON dosyasında değişiklik yapıldı mı
                username = profile_data.get("username", "")  # stats güncellemesi için

                # İşlenecek linkleri belirle (zaten indirilenleri hariç tut)
                links_to_process = []
                for item in links:
                    if isinstance(item, dict):
                        url = item.get("url", "")
                    elif isinstance(item, str):
                        url = item
                    else:
                        continue

                    normalized_url = normalize_url(url)
                    if normalized_url and normalized_url not in downloaded_urls:
                        links_to_process.append((item, url))

                if not links_to_process:
                    logging.info(f"Bu profilde indirilecek yeni link yok: {profile_file}")
                    continue

                logging.info(f"--- Profil: {profile_file}, İndirilecek link sayısı: {len(links_to_process)} ---")

                for item, url in links_to_process:
                    # URL'yi normalize et (? ve sonrasını kaldır)
                    url = url.split("?")[0].strip()

                    # URL zaten indirilmişse atla
                    if url in downloaded_urls:
                        continue

                    # Initialize variables for this iteration
                    success_download = False
                    caption = None
                    file_path = None
                    original_caption = None
                    shortcode = None
                    video_path = None
                    thumbnail_path = None
                    already_downloaded = False

                    # Instagram linki mi?
                    if "instagram.com" in url:
                        # Check if this Instagram link has already been downloaded (using normalized URLs)
                        for downloaded_item in profile_data.get("downloaded", []):
                            if normalize_url(downloaded_item.get("url", "")) == normalize_url(url):
                                already_downloaded = True
                                success_download = True
                                caption = downloaded_item.get("caption", "")
                                file_path = downloaded_item.get("file_path", "")
                                original_caption = downloaded_item.get("original_caption", "")
                                shortcode = downloaded_item.get("shortcode", "")
                                video_path = downloaded_item.get("video_path", "")
                                thumbnail_path = downloaded_item.get("thumbnail_path", "")
                                logging.info(f"Instagram link already downloaded, skipping: {url}")
                                break

                        if not already_downloaded:
                            from download import download_instagram_post_simple

                            # Use new simplified approach
                            success_download, shortcode, video_path, thumbnail_path, original_caption = download_instagram_post_simple(
                                url, output_dir
                            )

                            # For compatibility with existing code
                            caption = shortcode
                            file_path = video_path or thumbnail_path

                        # Eğer item bir dict ve description içeriyorsa, caption olarak onu kullan
                        if success_download and isinstance(item, dict) and "description" in item:
                            caption = item["description"]

                        if success_download and not already_downloaded:
                            # İndirilen dosyayı profil JSON'una ekle (only if not already downloaded)
                            downloaded = profile_data.get("downloaded", [])
                            downloaded.append({
                                "url": url,
                                "file_path": file_path,
                                "caption": caption,
                                "original_caption": original_caption
                            })
                            profile_data["downloaded"] = downloaded
                            downloaded_urls.add(normalize_url(url))  # URL'yi downloaded_urls setine ekle
                            updated = True
                            logging.info(f"{platform} linki başarıyla indirildi: {url}")
                        elif success_download and already_downloaded:
                            # Link already downloaded, just mark as processed
                            downloaded_urls.add(normalize_url(url))
                            updated = True
                            logging.info(f"{platform} linki zaten indirilmiş, işleme alındı: {url}")

                    # Twitter linki mi?
                    elif "twitter.com" in url or "x.com" in url:
                        # Check if this Twitter link has already been downloaded (using normalized URLs)
                        already_downloaded = False
                        for downloaded_item in profile_data.get("downloaded", []):
                            if normalize_url(downloaded_item.get("url", "")) == normalize_url(url):
                                already_downloaded = True
                                success_download = True
                                caption = downloaded_item.get("caption", "")
                                file_path = downloaded_item.get("file_path", "")
                                original_caption = downloaded_item.get("original_caption", "")
                                logging.info(f"Twitter link already downloaded, skipping: {url}")
                                break

                        if not already_downloaded:
                            # Download Twitter media with thumbnail creation for Instagram profiles
                            is_for_instagram = platform == "instagram"
                            success_download, caption, file_path, original_caption = download_twitter_media(
                                url, output_dir, ffmpeg_dir, for_instagram_profile=is_for_instagram
                            )

                            # Only create thumbnail if not already created by download_twitter_media
                            if success_download and file_path and not is_for_instagram:
                                # For Twitter profiles, create thumbnail separately
                                thumb = create_video_thumbnail(file_path)
                                logging.debug(f"Thumbnail created for Twitter profile: {thumb}")

                        # Eğer item bir dict ve description içeriyorsa, caption olarak onu kullan
                        if success_download and isinstance(item, dict) and "description" in item:
                            caption = item["description"]

                        if success_download and not already_downloaded:
                            # İndirilen dosyayı profil JSON'una ekle (only if not already downloaded)
                            downloaded = profile_data.get("downloaded", [])

                            # Create download entry with new structure
                            download_entry = {
                                "url": url,
                                "file_path": file_path,
                                "caption": caption,
                                "original_caption": original_caption
                            }

                            # Add shortcode-based paths for Instagram links
                            if "instagram.com" in url and 'shortcode' in locals():
                                download_entry["shortcode"] = shortcode
                                if 'video_path' in locals() and video_path:
                                    download_entry["video_path"] = video_path
                                if 'thumbnail_path' in locals() and thumbnail_path:
                                    download_entry["thumbnail_path"] = thumbnail_path

                            # Add thumbnail path for Twitter links
                            elif "twitter.com" in url or "x.com" in url:
                                if file_path and file_path.lower().endswith('.mp4'):
                                    # Calculate thumbnail path for Twitter videos
                                    thumbnail_path = os.path.splitext(file_path)[0] + ".jpg"
                                    if os.path.exists(thumbnail_path):
                                        download_entry["thumbnail_path"] = thumbnail_path

                            downloaded.append(download_entry)
                            profile_data["downloaded"] = downloaded
                            downloaded_urls.add(normalize_url(url))  # URL'yi downloaded_urls setine ekle
                            updated = True
                            logging.info(f"{platform} linki başarıyla indirildi: {url}")
                        elif success_download and already_downloaded:
                            # Link already downloaded, just mark as processed
                            downloaded_urls.add(normalize_url(url))
                            updated = True
                            logging.info(f"{platform} linki zaten indirilmiş, işleme alındı: {url}")

                    if success_download and caption:
                        # Link bir string ise, dict'e dönüştür
                        if isinstance(item, str):
                            new_item = {"url": url, "description": caption}
                            idx_in_links = links.index(item)
                            links[idx_in_links] = new_item
                            updated = True
                            logging.info(f"{platform} linki için caption güncellendi: {url}")

                    time.sleep(3)

                if updated:
                    profile_data["links"] = links
                    with open(profile_path, "w", encoding="utf-8") as f:
                        json.dump(profile_data, f, indent=4, ensure_ascii=False)

                    # Can barlarını anlık güncelle
                    update_can_bars_ui()

                # Her durumda downloaded listesini güncelleyelim
                with open(profile_path, "w", encoding="utf-8") as f:
                    json.dump(profile_data, f, indent=4, ensure_ascii=False)

                # Can barlarını anlık güncelle
                update_can_bars_ui()

            except Exception as e:
                logging.error(f"Profil dosyası okunurken hata oluştu ({profile_file}): {str(e)}")


def upload_instagram_video_for_profile(profile_path, profile_data, instagram_output_dir):
    """
    New Instagram posting implementation using the provided instagram_poster.py logic.
    Integrates with SorcerioModules profile system and maintains compatibility.
    """
    try:
        username = profile_data.get("username")
        password = profile_data.get("password")
        if not username or not password:
            logging.error("Instagram profilinde kullanıcı adı veya şifre eksik.")
            return False

        downloaded = profile_data.get("downloaded", [])
        downloaded = [item for item in downloaded if os.path.exists(item.get("file_path", ""))]
        profile_data["downloaded"] = downloaded

        # Update JSON after cleanup
        with open(profile_path, "w", encoding="utf-8") as f:
            json.dump(profile_data, f, indent=4, ensure_ascii=False)
        update_stats_display_html()

        # Update can bars
        update_can_bars_ui()

        if not downloaded:
            logging.error("downloaded listesi boş.")
            return False

        first_item = downloaded[0]

        # Handle both legacy and new shortcode-based file paths
        video_path = first_item.get("video_path") or first_item.get("file_path")
        thumbnail_path = first_item.get("thumbnail_path")

        # If no specific thumbnail path, try to derive it
        if not thumbnail_path and video_path:
            if first_item.get("shortcode"):
                # For shortcode-based files, use {shortcode}.jpg
                base_dir = os.path.dirname(video_path)
                thumbnail_path = os.path.join(base_dir, f"{first_item['shortcode']}.jpg")
            else:
                # Legacy approach
                thumbnail_path = os.path.splitext(video_path)[0] + ".jpg"

        video_path = os.path.normpath(video_path) if video_path else None
        caption = first_item.get("original_caption") or first_item.get("caption", "")

        if not video_path or not os.path.exists(video_path):
            logging.error(f"Video dosyası bulunamadı: {video_path}")
            return False

        # Use new Instagram posting implementation
        success = upload_instagram_item_new(username, password, video_path, thumbnail_path, caption)

        if success:
            logging.info(f"Instagram video başarıyla yüklendi: {os.path.basename(video_path)}")
            original_url_to_remove = first_item.get("url")

            # Remove from downloaded list
            if first_item in profile_data["downloaded"]:
                profile_data["downloaded"].remove(first_item)
            else:
                profile_data["downloaded"] = [d for d in profile_data["downloaded"] if
                                            d.get("url") != original_url_to_remove]

            # Remove from links list
            if "links" in profile_data and original_url_to_remove:
                profile_data["links"] = [
                    link for link in profile_data["links"]
                    if (link if isinstance(link, str) else link.get("url", "")) != original_url_to_remove
                ]

            # Update JSON
            with open(profile_path, "w", encoding="utf-8") as f:
                json.dump(profile_data, f, indent=4, ensure_ascii=False)
            logging.info(f"Profil JSON güncellendi (paylaşım sonrası): {profile_path}")

            # Update can bars
            update_can_bars_ui()

            # Post-sharing cleanup - delete files after successful upload
            try:
                # Collect protected files from all profiles
                base_dir = os.path.dirname(os.path.abspath(__file__))
                config_dir = os.path.join(base_dir, "configuration")
                all_protected_files = set()

                for platform in ["instagram", "twitter"]:
                    platform_dir = os.path.join(config_dir, platform)
                    if os.path.exists(platform_dir):
                        for profile_file in os.listdir(platform_dir):
                            if profile_file.endswith(".json"):
                                profile_json_path = os.path.join(platform_dir, profile_file)
                                protected = protect_media_files(profile_json_path)
                                all_protected_files.update(protected)

                # Safely delete video file (post-sharing cleanup)
                if safe_file_delete(video_path, all_protected_files, allow_deletion=True):
                    logging.info(f"Video dosyası güvenli bir şekilde silindi (paylaşım sonrası): {video_path}")

                # Safely delete thumbnail (post-sharing cleanup)
                if thumbnail_path and safe_file_delete(thumbnail_path, all_protected_files, allow_deletion=True):
                    logging.info(f"Thumbnail güvenli bir şekilde silindi (paylaşım sonrası): {thumbnail_path}")

                    # Clean thumbnail from JSON
                    profile_data["downloaded"] = [
                        item for item in profile_data["downloaded"]
                        if item.get("file_path") != thumbnail_path and
                           not (item.get("is_thumbnail", False) and item.get("file_path", "").endswith(".jpg"))
                    ]

                    # Update JSON
                    with open(profile_path, "w", encoding="utf-8") as f:
                        json.dump(profile_data, f, indent=4, ensure_ascii=False)
                    logging.info(f"Thumbnail JSON'dan temizlendi: {thumbnail_path}")
            except Exception as e:
                logging.error(f"Güvenli dosya silme işleminde hata: {e}")

            return True
        else:
            logging.error("Instagram video yükleme başarısız.")
            return False

    except Exception as e:
        logging.error(f"Instagram video yükleme hatası: {str(e)}")
        return False


def upload_twitter_video_for_profile(profile_path, profile_data, twitter_output_dir):
    driver = None
    try:
        username = profile_data.get("username")
        password = profile_data.get("password")

        if not username or not password:
            logging.error("Twitter profilinde kullanıcı adı veya şifre eksik.")
            return False

        downloaded = profile_data.get("downloaded", [])
        downloaded = [item for item in downloaded if os.path.exists(item.get("file_path", ""))]
        profile_data["downloaded"] = downloaded

        # Verilen profil path'i kullan
        with open(profile_path, "w", encoding="utf-8") as f:
            json.dump(profile_data, f, indent=4, ensure_ascii=False)

        if not downloaded:
            logging.error("downloaded listesi boş. Yüklenecek video yok.")
            return False

        first_item = downloaded[0]

        # Handle both legacy and new shortcode-based file paths
        video_path = first_item.get("video_path") or first_item.get("file_path")
        video_path = os.path.normpath(video_path) if video_path else None

        final_text = first_item.get("original_caption") or first_item.get("caption", "")
        logging.info(f"Tweet metni: {final_text}")

        if not os.path.exists(video_path):
            logging.error(f"Video dosyası bulunamadı: {video_path}")
            profile_data["downloaded"].remove(first_item)
            with open(profile_path, "w", encoding="utf-8") as f:
                json.dump(profile_data, f, indent=4, ensure_ascii=False)
            return False

        # Create driver with better error handling
        try:
            logging.info(f"Twitter driver oluşturuluyor: {username}")
            driver = create_new_driver_for_account(username)
        except Exception as e:
            logging.error(f"Twitter driver oluşturulamadı ({username}): {e}")
            return False

        # Login with better error handling
        try:
            if not login_twitter_account(driver, username, password):
                logging.error(f"Twitter giriş başarısız: {username}")
                if driver:
                    driver.quit()
                return False
        except Exception as e:
            logging.error(f"Twitter giriş hatası ({username}): {e}")
            if driver:
                driver.quit()
            return False

        try:
            logging.info(f"{os.path.basename(video_path)} dosyası yükleniyor...")
            driver.get("https://x.com/compose/post")
            time.sleep(8)

            file_input = WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='file']"))
            )
            file_input.send_keys(video_path)
            logging.info("Video yüklemesi başladı, 40 saniye bekleniyor...")
            time.sleep(40)

            tweet_box = WebDriverWait(driver, 20).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "div[data-testid='tweetTextarea_0']"))
            )
            driver.execute_script("arguments[0].click();", tweet_box)
            time.sleep(1)
            tweet_box.clear()
            time.sleep(1)
            tweet_box.send_keys(final_text)

            tweet_button_js = """
            var btn = document.querySelector('button[data-testid="tweetButton"], div[data-testid="tweetButton"]');
            if(btn){
                btn.click();
                return true;
            } else {
                return false;
            }
            """
            result = driver.execute_script(tweet_button_js)

            if not result:
                raise Exception("Tweet butonu bulunamadı!")
            logging.info("Tweet gönderildi!")
            time.sleep(25)  # Tweetin işlenmesi için bekleme süresi

            # Paylaşım başarılı, şimdi temizlik yapalım
            original_url_to_remove = first_item.get("url")

            # 1. Güvenli dosya silme işlemi
            try:
                # Thumbnail yolunu kontrol et - handle shortcode-based naming
                thumbnail_path = first_item.get("thumbnail_path")
                if not thumbnail_path:
                    if first_item.get("shortcode"):
                        # For shortcode-based files, use {shortcode}.jpg
                        base_dir = os.path.dirname(video_path)
                        thumbnail_path = os.path.join(base_dir, f"{first_item['shortcode']}.jpg")
                    else:
                        # Legacy approach - derive from video path
                        thumbnail_path = os.path.splitext(video_path)[0] + ".jpg"

                logging.info(f"Post-sharing cleanup: Video={video_path}, Thumbnail={thumbnail_path}")

                # Collect protected files from OTHER profiles (not the current one being processed)
                base_dir = os.path.dirname(os.path.abspath(__file__))
                config_dir = os.path.join(base_dir, "configuration")
                all_protected_files = set()

                for platform in ["instagram", "twitter"]:
                    platform_dir = os.path.join(config_dir, platform)
                    if os.path.exists(platform_dir):
                        for profile_file in os.listdir(platform_dir):
                            if profile_file.endswith(".json"):
                                profile_json_path = os.path.join(platform_dir, profile_file)
                                # Skip the current profile to avoid protecting files we want to delete
                                if profile_json_path != profile_path:
                                    protected = protect_media_files(profile_json_path)
                                    all_protected_files.update(protected)

                # Force delete video file (post-sharing cleanup)
                video_deleted = False
                try:
                    if os.path.exists(video_path):
                        os.remove(video_path)
                        video_deleted = True
                        logging.info(f"Twitter video başarıyla silindi: {video_path}")
                except Exception as e:
                    logging.error(f"Twitter video silinirken hata: {video_path} - {e}")

                # Force delete thumbnail (post-sharing cleanup)
                thumbnail_deleted = False
                try:
                    if thumbnail_path and os.path.exists(thumbnail_path):
                        os.remove(thumbnail_path)
                        thumbnail_deleted = True
                        logging.info(f"Twitter thumbnail başarıyla silindi: {thumbnail_path}")
                except Exception as e:
                    logging.error(f"Twitter thumbnail silinirken hata: {thumbnail_path} - {e}")

            except Exception as e:
                logging.error(f"Güvenli dosya silme işleminde hata: {video_path} - {str(e)}")

            # 2. 'downloaded' listesinden kaldır
            if first_item in profile_data["downloaded"]:
                profile_data["downloaded"].remove(first_item)
            else:  # URL ile eşleşeni bul ve kaldır
                profile_data["downloaded"] = [d for d in profile_data["downloaded"] if
                                              d.get("url") != original_url_to_remove]

            # 3. 'links' listesinden kaldır (eğer varsa)
            if "links" in profile_data and original_url_to_remove:
                profile_data["links"] = [
                    link for link in profile_data["links"]
                    if (link if isinstance(link, str) else link.get("url", "")) != original_url_to_remove
                ]

            # 4. JSON dosyasını güncelle
            with open(profile_path, "w", encoding="utf-8") as f:
                json.dump(profile_data, f, indent=4, ensure_ascii=False)
            logging.info(f"Profil JSON güncellendi (Twitter paylaşımı sonrası): {profile_path}")

            # Can barlarını anlık güncelle
            update_can_bars_ui()

            # Arayüz güncellemesi için MainWindow'daki update_stats periyodik olarak çalışacaktır.
            # Anlık güncelleme için, MainWindow'a bir sinyal gönderilebilir veya doğrudan bir metod çağrılabilir.

            return True
        except Exception as e:
            logging.error(f"Twitter video yükleme veya tweet atma hatası: {str(e)}")
            return False
        finally:
            if driver:
                driver.quit()
    except Exception as e:
        logging.error(f"upload_twitter_video_for_profile fonksiyonunda genel hata: {str(e)}")
        return False


def upload_random_instagram_video(instagram_output_dir):
    """
    Instagram video paylaşım fonksiyonu:
    - 'instagramdownloaded' klasöründen rastgele bir video seçer
    - Dosya adını (uzantısız) açıklama olarak kullanır
    - Videoyu Instagram'a yükler
    - Uses new Instagram posting implementation
    """
    try:
        # Get credentials from first available Instagram profile
        base_dir = os.path.dirname(os.path.abspath(__file__))
        config_dir = os.path.join(base_dir, "configuration", "instagram")
        profile_files = [f for f in os.listdir(config_dir) if f.endswith(".json")]

        username = None
        password = None

        for profile_file in profile_files:
            try:
                profile_path = os.path.join(config_dir, profile_file)
                with open(profile_path, "r", encoding="utf-8") as f:
                    profile_data = json.load(f)

                profile_username = profile_data.get("username", "").strip()
                profile_password = profile_data.get("password", "").strip()

                if profile_username and profile_password:
                    username = profile_username
                    password = profile_password
                    break
            except Exception as e:
                logging.error(f"Profil dosyası okuma hatası {profile_file}: {e}")
                continue

        if not username or not password:
            logging.error("Instagram profil dosyalarında geçerli kullanıcı adı/şifre bulunamadı.")
            return False

        videos = [
            os.path.join(instagram_output_dir, f)
            for f in os.listdir(instagram_output_dir)
            if f.lower().endswith((".mp4", ".mov", ".mkv", ".webm"))
        ]
        if not videos:
            logging.error("instagramdownloaded klasöründe yüklenecek video bulunamadı.")
            return False

        selected_video = random.choice(videos)
        logging.info(f"Seçilen video: {selected_video}")
        video_caption = os.path.splitext(os.path.basename(selected_video))[0]
        logging.info(f"Açıklama olarak kullanılacak: {video_caption}")

        # Try to find corresponding thumbnail
        thumbnail_path = os.path.splitext(selected_video)[0] + ".jpg"
        if not os.path.exists(thumbnail_path):
            thumbnail_path = None

        # Use new Instagram posting implementation
        success = upload_instagram_item_new(username, password, selected_video, thumbnail_path, video_caption)

        if success:
            logging.info(f"Video başarıyla Instagram'a yüklendi: {os.path.basename(selected_video)}")
            # Delete the uploaded video file
            try:
                os.remove(selected_video)
                logging.info(f"Yüklenen video dosyası silindi: {selected_video}")
                if thumbnail_path and os.path.exists(thumbnail_path):
                    os.remove(thumbnail_path)
                    logging.info(f"Yüklenen thumbnail silindi: {thumbnail_path}")
            except Exception as e:
                logging.warning(f"Dosya silme hatası: {e}")
            return True
        else:
            logging.error("Instagram video yükleme başarısız.")
            return False

    except Exception as e:
        logging.error(f"Instagram video yükleme hatası: {str(e)}")
        return False


def upload_random_twitter_video(twitter_output_dir):
    """
    Twitter video paylaşım fonksiyonu:
    - 'twitterdownloaded' klasöründen rastgele bir video seçer
    - Dosya adını (uzantısız) tweet metni olarak kullanır
    - Videoyu Twitter'a yükler
    """
    driver = None
    try:
        # Twitter hesap bilgilerini al (örnek olarak ilk profil dosyasından)
        base_dir = os.path.dirname(os.path.abspath(__file__))
        config_dir = os.path.join(base_dir, "configuration", "twitter")
        profile_files = [f for f in os.listdir(config_dir) if f.endswith(".json")]

        if not profile_files:
            logging.error("Twitter profil dosyası bulunamadı.")
            return False

        # İlk profil dosyasını kullan
        profile_path = os.path.join(config_dir, profile_files[0])
        with open(profile_path, "r", encoding="utf-8") as f:
            profile_data = json.load(f)

        username = profile_data.get("username")
        password = profile_data.get("password")

        if not username or not password:
            logging.error("Twitter profilinde kullanıcı adı veya şifre eksik.")
            return False

        # Twitterdownloaded klasöründeki videoları listele
        videos = [
            os.path.join(twitter_output_dir, f)
            for f in os.listdir(twitter_output_dir)
            if f.lower().endswith((".mp4", ".mov", ".mkv", ".webm"))
        ]

        if not videos:
            logging.error("twitterdownloaded klasöründe yüklenecek video bulunamadı.")
            return False

        selected_video = random.choice(videos)
        logging.info(f"Seçilen video: {selected_video}")

        # Video açıklamasını dosya adından al
        tweet_text = os.path.splitext(os.path.basename(selected_video))[0]
        logging.info(f"Tweet metni olarak kullanılacak: {tweet_text}")

        # Twitter'a giriş yap ve videoyu paylaş
        try:
            logging.info(f"Random Twitter upload için driver oluşturuluyor: {username}")
            driver = create_new_driver_for_account(username)
        except Exception as e:
            logging.error(f"Random Twitter upload için driver oluşturulamadı ({username}): {e}")
            return False

        try:
            if not login_twitter_account(driver, username, password):
                logging.error("Twitter'a giriş yapılamadı.")
                if driver:
                    driver.quit()
                return False
        except Exception as e:
            logging.error(f"Random Twitter upload giriş hatası ({username}): {e}")
            if driver:
                driver.quit()
            return False

        try:
            logging.info(f"{os.path.basename(selected_video)} dosyası yükleniyor...")
            driver.get("https://x.com/compose/post")
            time.sleep(8)

            file_input = WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='file']"))
            )
            file_input.send_keys(selected_video)
            logging.info("Video yüklemesi başladı, 40 saniye bekleniyor...")
            time.sleep(40)

            tweet_box = WebDriverWait(driver, 20).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "div[data-testid='tweetTextarea_0']"))
            )
            driver.execute_script("arguments[0].click();", tweet_box)
            time.sleep(1)
            tweet_box.clear()
            time.sleep(1)
            tweet_box.send_keys(tweet_text)

            tweet_button_js = """
            var btn = document.querySelector('button[data-testid="tweetButton"], div[data-testid="tweetButton"]');
            if(btn){
                btn.click();
                return true;
            } else {
                return false;
            }
            """
            result = driver.execute_script(tweet_button_js)

            if not result:
                raise Exception("Tweet butonu bulunamadı!")
            logging.info("Tweet gönderildi!")
            time.sleep(25)  # Tweetin işlenmesi için bekleme süresi

            # Paylaşım başarılı, şimdi güvenli temizlik yapalım
            try:
                # Thumbnail yolunu hesapla
                thumbnail_path = os.path.splitext(selected_video)[0] + ".jpg"

                logging.info(f"Random Twitter post cleanup: Video={selected_video}, Thumbnail={thumbnail_path}")

                # Force delete video file (post-sharing cleanup)
                try:
                    if os.path.exists(selected_video):
                        os.remove(selected_video)
                        logging.info(f"Random Twitter video başarıyla silindi: {selected_video}")
                except Exception as e:
                    logging.error(f"Random Twitter video silinirken hata: {selected_video} - {e}")

                # Force delete thumbnail (post-sharing cleanup)
                try:
                    if os.path.exists(thumbnail_path):
                        os.remove(thumbnail_path)
                        logging.info(f"Random Twitter thumbnail başarıyla silindi: {thumbnail_path}")
                except Exception as e:
                    logging.error(f"Random Twitter thumbnail silinirken hata: {thumbnail_path} - {e}")

            except Exception as e:
                logging.error(f"Güvenli dosya silme işleminde hata: {selected_video} - {str(e)}")

            return True
        except Exception as e:
            logging.error(f"Twitter video yükleme veya tweet atma hatası: {str(e)}")
            return False
        finally:
            if driver:
                driver.quit()
    except Exception as e:
        logging.error(f"upload_random_twitter_video fonksiyonunda genel hata: {str(e)}")
        if driver:
            driver.quit()
        return False


def should_process_profile(profile_data):
    """Profilin şu anki saatte işlenip işlenmeyeceğini kontrol eder."""
    now = datetime.now()
    current_day = now.strftime("%A").lower()  # "monday", "tuesday" vs.
    scheduled_times = profile_data.get("schedule", {}).get(current_day, [])

    for time_str in scheduled_times:
        try:
            scheduled_time = datetime.strptime(time_str, "%H:%M").time()
            if (now.hour == scheduled_time.hour) and (now.minute == scheduled_time.minute):
                return True
        except ValueError:
            continue
    return False


def process_profiles_with_schedule():
    """
    Her dakika çalışıp, profil JSON'larındaki schedule saatlerine bakarak
    işlenecek profilleri tespit eder ve onların linklerini işler.
    """
    # Aşağıdaki BASE_DIR, sizin projenizde 'base_dir' olarak da geçiyor olabilir
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    platforms = ["instagram", "twitter"]

    # Örnek olarak ffmpeg yolunu vs. buradan da alabiliriz
    videos_dir = os.path.join(BASE_DIR, "videos")
    temp_dir = os.path.join(videos_dir, "temp")
    ffmpeg_dir = download_ffmpeg()

    # İndirme çıktıları
    instagram_output_dir = os.path.join(videos_dir, "instagramdownloaded")
    twitter_output_dir = os.path.join(videos_dir, "twitterdownloaded")

    for platform in platforms:
        config_dir = os.path.join(BASE_DIR, "configuration", platform)
        if not os.path.exists(config_dir):
            continue

        for profile_file in os.listdir(config_dir):
            if not profile_file.endswith(".json"):
                continue
            profile_path = os.path.join(config_dir, profile_file)
            with open(profile_path, "r", encoding="utf-8") as f:
                profile_data = json.load(f)

            # Zaman kontrolü
            if should_process_profile(profile_data):
                # Bu profili işleyelim
                if platform == "instagram":
                    # Instagram profili içindeki hem instagram hem twitter linklerini işle
                    process_profile_links("instagram", instagram_output_dir, temp_dir, ffmpeg_dir)
                    process_profile_links("twitter", instagram_output_dir, temp_dir, ffmpeg_dir)
                elif platform == "twitter":
                    # Twitter profili içindeki hem twitter hem instagram linklerini işle
                    process_profile_links("twitter", twitter_output_dir, temp_dir, ffmpeg_dir)
                    process_profile_links("instagram", twitter_output_dir, temp_dir, ffmpeg_dir)


def start_scheduler():
    """
    Arka planda her 1 dakikada bir 'process_profiles_with_schedule' fonksiyonunu tetikler.
    """
    scheduler = BackgroundScheduler()
    scheduler.add_job(process_profiles_with_schedule, 'interval', minutes=1)
    scheduler.start()


def initialize_sessions_on_startup():
    """
    DEPRECATED: Old Instagram session initialization.
    Now using on-demand session management with get_instagram_client().
    This function is kept for compatibility but does minimal work.
    """
    logging.info("Instagram session initialization (new implementation - on-demand)")
    # The new implementation handles sessions on-demand, so no startup initialization needed
    pass


def initialize_twitter_sessions_on_startup():
    """
    Program başlangıcında Twitter session (Selenium profil) klasörlerini kontrol eder:
    - Aktif profiller için yeni oturum oluşturur
    - Silinmiş profiller için, 24 saatten taze olanları saklar, eskileri temizler
    """
    logging.info("Tüm Twitter session'ları (Selenium profilleri) başlangıçta kontrol ediliyor...")
    base_project_dir = os.path.dirname(os.path.abspath(__file__))
    twitter_config_dir = os.path.join(base_project_dir, "configuration", "twitter")
    session_dir = os.path.join(twitter_config_dir, "sessions")
    os.makedirs(session_dir, exist_ok=True)

    if not os.path.exists(twitter_config_dir):
        logging.warning(f"Twitter konfigürasyon dizini bulunamadı: {twitter_config_dir}")
        return

    # Aktif profilleri topla
    profile_files = [f for f in os.listdir(twitter_config_dir) if f.endswith(".json")]
    logging.info(f"Yeniden giriş için {len(profile_files)} Twitter profili bulundu.")

    # Aktif kullanıcı adlarını topla
    mevcut_userlar = set()
    for profile_file_name in profile_files:
        profile_path = os.path.join(twitter_config_dir, profile_file_name)
        try:
            with open(profile_path, "r", encoding="utf-8") as f:
                profile_data = json.load(f)
            username = profile_data.get("username", "").strip()
            if username:
                mevcut_userlar.add(username)
        except Exception as e:
            logging.error(f"Profil JSON okunurken hata ({profile_file_name}): {e}")

    # Mevcut profil klasörlerini kontrol et
    now = datetime.now()
    profile_dirs = [d for d in os.listdir(base_project_dir) if d.startswith("chrome_profile_")]

    for profile_dir in profile_dirs:
        username = profile_dir.replace("chrome_profile_", "")
        profile_path = os.path.join(base_project_dir, profile_dir)

        # Kullanıcı hala aktif mi?
        if username not in mevcut_userlar:
            # Session meta dosyasını kontrol et
            meta_file = os.path.join(session_dir, f"{username}_session.meta.json")
            session_age_hours = 25  # Varsayılan olarak 24 saatten eski kabul et

            if os.path.exists(meta_file):
                try:
                    with open(meta_file, "r", encoding="utf-8") as f:
                        meta = json.load(f)
                    created_str = meta.get("created")
                    if created_str:
                        created = datetime.strptime(created_str, "%Y-%m-%d %H:%M:%S")
                        session_age_hours = (now - created).total_seconds() / 3600
                except Exception as e:
                    logging.error(f"Meta dosyası okunurken hata: {meta_file} - {e}")
            else:
                # Meta dosyası yoksa, klasör oluşturulma zamanını kullan
                try:
                    created = datetime.fromtimestamp(os.path.getctime(profile_path))
                    session_age_hours = (now - created).total_seconds() / 3600

                    # Meta dosyası oluştur
                    meta = {
                        "created": created.strftime("%Y-%m-%d %H:%M:%S"),
                        "last_used": now.strftime("%Y-%m-%d %H:%M:%S"),
                        "next_refresh": ""
                    }
                    with open(meta_file, "w", encoding="utf-8") as f:
                        json.dump(meta, f, indent=4, ensure_ascii=False)
                except Exception as e:
                    logging.error(f"Profil klasörü yaşı kontrol edilirken hata: {profile_path} - {e}")

            # 24 saatten eski ise sil, değilse sakla
            if session_age_hours >= 24:
                try:
                    # Silmeden önce ilgili driver'ın kapalı olduğundan emin ol
                    try:
                        import psutil
                        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                            if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                                if profile_path in ' '.join(proc.info['cmdline']):
                                    proc.terminate()
                                    proc.wait(timeout=5)
                    except Exception as e:
                        logging.warning(f"Profil silme öncesi chrome işlemi kapatılamadı: {e}")

                    shutil.rmtree(profile_path)
                    logging.info(f"24 saatten eski Twitter profil klasörü silindi: {profile_path}")

                    # Meta dosyasını da sil
                    if os.path.exists(meta_file):
                        os.remove(meta_file)
                except Exception as e:
                    logging.error(f"Twitter profil klasörü silinirken hata: {profile_path} - {e}")
            else:
                logging.info(f"Twitter profil klasörü 24 saatten taze, saklanıyor: {profile_path} ({session_age_hours:.1f} saat)")

    # Aktif profiller için yeni oturum oluştur
    for profile_file_name in profile_files:
        profile_path = os.path.join(twitter_config_dir, profile_file_name)
        username_from_file = ""
        try:
            with open(profile_path, "r", encoding="utf-8") as f:
                profile_data = json.load(f)

            username = profile_data.get("username", "").strip()
            password = profile_data.get("password", "").strip()
            username_from_file = username  # Keep track for folder deletion

            if not username or not password:
                logging.warning(f"Profil {profile_file_name} için kullanıcı adı veya şifre eksik, atlanıyor.")
                continue

            # Yeni driver oluştur ve giriş yap (bu, profil klasörünü yeniden oluşturacak)
            logging.info(f"Twitter'a giriş yapılıyor: {username} ({profile_file_name})")
            driver = None
            try:
                driver = create_new_driver_for_account(username)
                if login_twitter_account(driver, username, password):
                    logging.info(
                        f"Twitter'a giriş başarılı ve session (profil klasörü) oluşturuldu/güncellendi: {username}")

                    # Session meta dosyasını güncelle
                    meta_file = os.path.join(session_dir, f"{username}_session.meta.json")
                    meta = {
                        "created": now.strftime("%Y-%m-%d %H:%M:%S"),
                        "last_used": now.strftime("%Y-%m-%d %H:%M:%S"),
                        "next_refresh": ""
                    }
                    with open(meta_file, "w", encoding="utf-8") as f:
                        json.dump(meta, f, indent=4, ensure_ascii=False)
                else:
                    logging.warning(f"Twitter'a giriş başarısız: {username}")
            except Exception as login_exc:
                logging.error(f"Twitter için driver oluşturma veya giriş hatası ({username}): {str(login_exc)}")
            finally:
                if driver:
                    driver.quit()

        except Exception as e:
            logging.error(f"Twitter profil dosyası ({profile_file_name}) işlenirken genel hata: {str(e)}")

        time.sleep(random.uniform(3, 7))  # Giriş denemeleri arasında daha uzun bekleme

    logging.info("Twitter session (Selenium profilleri) kontrol işlemi tamamlandı.")


def reload_scheduler(self):
    from scheduler_utils import reload_scheduler_helper
    reload_scheduler_helper(self)


def threaded_download_all_profiles_links(self):
    thread = threading.Thread(target=download_all_profiles_links)
    thread.start()


def threaded_upload_all_profiles_links(self):
    thread = threading.Thread(target=upload_all_profiles_links)
    thread.start()


def threaded_process_profile_links(self, platform, output_dir, temp_dir, ffmpeg_dir):
    thread = threading.Thread(
        target=self.process_profile_links,
        args=(platform, output_dir, temp_dir, ffmpeg_dir),
    )
    thread.daemon = True
    thread.start()


@pyqtSlot()
def trigger_stats_update(self):
    logging.info("ProfileEditorDialog'dan tetiklenen anlık istatistik ve arayüz güncellemesi çağrıldı.")
    # update_stats zaten check_profiles'ı çağırıyor ve sonra HTML'i güncelliyor.
    # Bu, yeni eklenen/güncellenen profilin istatistiklerinin çekilmesini
    # ve arayüzde gösterilmesini sağlamalı.
    self.update_stats()


def on_load_finished(self):
    self.set_status_bar_color(False)


insta_session_thread = threading.Thread(target=initialize_sessions_on_startup, daemon=True)
twitter_session_thread = threading.Thread(target=initialize_twitter_sessions_on_startup, daemon=True)


def twitter_session_refresh_daemon():
    import time, os, json, random, shutil
    from datetime import datetime, timedelta
    base_config_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "configuration", "twitter")
    session_dir = os.path.join(base_config_dir, "sessions")
    while True:
        try:
            if not os.path.exists(session_dir):
                os.makedirs(session_dir, exist_ok=True)
                time.sleep(3600)
                continue

            # Aktif profillerdeki kullanıcı adlarını topla
            base_dir = os.path.dirname(os.path.abspath(__file__))
            twitter_config_dir = os.path.join(base_dir, "configuration", "twitter")
            mevcut_userlar = set()

            if os.path.exists(twitter_config_dir):
                for profile_file in os.listdir(twitter_config_dir):
                    if not profile_file.endswith(".json"):
                        continue
                    profile_path = os.path.join(twitter_config_dir, profile_file)
                    try:
                        with open(profile_path, "r", encoding="utf-8") as f:
                            data = json.load(f)
                        username = data.get("username", "").strip()
                        if username:
                            mevcut_userlar.add(username)
                    except Exception as e:
                        logging.warning(f"Twitter profil JSON okunurken hata ({profile_file}): {e}")

            # Session meta dosyalarını kontrol et
            for meta_file in os.listdir(session_dir):
                if not meta_file.endswith("_session.meta.json"): continue
                meta_path = os.path.join(session_dir, meta_file)
                try:
                    with open(meta_path, "r", encoding="utf-8") as mf:
                        meta = json.load(mf)
                    created = meta.get("created")
                    next_refresh = meta.get("next_refresh")
                    username = meta_file.replace("_session.meta.json", "")
                    if created:
                        created_dt = datetime.strptime(created, "%Y-%m-%d %H:%M:%S")
                        now = datetime.now()

                        # Profil hala aktif mi kontrol et
                        if username in mevcut_userlar:
                            # Yenileme zamanı gelmiş mi?
                            if next_refresh and now >= datetime.strptime(next_refresh, "%Y-%m-%d %H:%M:%S"):
                                # Oturum klasörünü sil
                                base_project_dir = os.path.dirname(os.path.abspath(__file__))
                                user_profile_dir_path = os.path.join(base_project_dir, f"chrome_profile_{username}")
                                if os.path.exists(user_profile_dir_path):
                                    try:
                                        shutil.rmtree(user_profile_dir_path)
                                    except Exception:
                                        pass
                                # Meta güncelle
                                meta["created"] = now.strftime("%Y-%m-%d %H:%M:%S")
                                meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
                                meta["next_refresh"] = ""
                                with open(meta_path, "w", encoding="utf-8") as mf:
                                    json.dump(meta, mf, indent=4, ensure_ascii=False)
                        else:
                            # Profil artık aktif değil, session yaşını kontrol et
                            session_age_hours = (now - created_dt).total_seconds() / 3600

                            # 24 saatten eski ise sil, değilse sakla
                            if session_age_hours >= 24:
                                # Oturum klasörünü ve meta dosyasını sil
                                base_project_dir = os.path.dirname(os.path.abspath(__file__))
                                user_profile_dir_path = os.path.join(base_project_dir, f"chrome_profile_{username}")
                                if os.path.exists(user_profile_dir_path):
                                    try:
                                        shutil.rmtree(user_profile_dir_path)
                                        logging.info(f"24 saatten eski Twitter profil klasörü silindi: {user_profile_dir_path}")
                                    except Exception as e:
                                        logging.error(f"Twitter profil klasörü silinirken hata: {str(e)}")

                                # Meta dosyasını sil
                                try:
                                    os.remove(meta_path)
                                    logging.info(f"24 saatten eski Twitter session meta dosyası silindi: {meta_file}")
                                except Exception as e:
                                    logging.error(f"Twitter session meta dosyası silinirken hata: {str(e)}")
                            else:
                                logging.info(f"Twitter session 24 saatten taze, saklanıyor: {meta_file} ({session_age_hours:.1f} saat)")
                except Exception as e:
                    logging.error(f"Twitter session kontrolü sırasında hata: {str(e)}")
                    continue
        except Exception as e:
            logging.error(f"Twitter session daemon genel hata: {str(e)}")
            pass
        time.sleep(3600)  # Her saat başı kontrol et


twitter_session_refresh_thread = threading.Thread(target=twitter_session_refresh_daemon, daemon=True)


def upload_all_profiles_links():
    """
    Tüm platformlardaki profillerin JSON içindeki 'links' listesini indirip işler.
    Link eklendiğinde anında tetiklenecek ve işlenmiş linkleri paylaşıma hazırlayacak.
    """
    base_dir = os.path.dirname(os.path.abspath(__file__))
    videos_dir = os.path.join(base_dir, "videos")
    temp_dir = os.path.join(videos_dir, "temp")
    ffmpeg_dir = download_ffmpeg()

    # Çıktı klasörleri
    instagram_output_dir = os.path.join(videos_dir, "instagramdownloaded")
    twitter_output_dir = os.path.join(videos_dir, "twitterdownloaded")

    # Instagram ve Twitter platformlarındaki profillerin linklerini işle
    process_profile_links("instagram", instagram_output_dir, temp_dir, ffmpeg_dir)
    process_profile_links("twitter", twitter_output_dir, temp_dir, ffmpeg_dir)

    logging.info("Tüm profillerin linkleri işlendi.")

    # İstatistikleri güncelle
    update_stats_display_html()


# ===== NEW INSTAGRAM POSTING IMPLEMENTATION =====
# Based on the provided instagram_poster.py code, adapted for SorcerioModules

class InstagramUploader:
    """Instagram uploader with anti-detection measures"""

    def __init__(self, username, password, debug=False):
        self.username = username
        self.password = password
        self.debug = debug
        self.client = None
        self.session_file = f"session_{username}.json"
        self.last_action_time = 0
        self.upload_count = 0

    def human_delay(self, min_seconds=2, max_seconds=7):
        """Add random delay to mimic human behavior"""
        # Ensure minimum time between actions
        elapsed = time.time() - self.last_action_time
        if elapsed < min_seconds:
            time.sleep(min_seconds - elapsed)

        # Add random delay
        delay = random.uniform(min_seconds, max_seconds)
        logging.debug(f"Adding human-like delay: {delay:.2f}s")
        time.sleep(delay)
        self.last_action_time = time.time()

    def generate_device_id(self):
        """Generate a device ID manually"""
        return str(uuid.uuid4())

    def login(self):
        """Login to Instagram with session management"""
        self.client = Client()
        self.client.delay_range = [1, 3]  # Set built-in delay range

        # Set session file path to SorcerioModules session directory
        base_dir = os.path.dirname(os.path.abspath(__file__))
        session_dir = os.path.join(base_dir, "configuration", "instagram", "sessions")
        os.makedirs(session_dir, exist_ok=True)
        self.session_file = os.path.join(session_dir, f"{self.username}_session.json")

        try:
            # Try to load existing session
            if os.path.exists(self.session_file):
                logging.info(f"Loading session from {self.session_file}")
                self.client.load_settings(self.session_file)

                # Verify session is valid
                try:
                    self.client.get_timeline_feed()
                    logging.info("Session is valid")
                    return True
                except LoginRequired:
                    logging.info("Session expired, logging in again")

            # Perform new login with anti-detection measures
            logging.info(f"Logging in as {self.username}")

            # Set realistic device and app info
            self.client.user_agent = "Instagram 219.0.0.12.117 Android"

            # Use the client's generate_uuid method if available, otherwise use our own
            if hasattr(self.client, 'generate_uuid'):
                self.client.phone_id = self.client.generate_uuid()
                self.client.uuid = self.client.generate_uuid()
            else:
                self.client.phone_id = str(uuid.uuid4())
                self.client.uuid = str(uuid.uuid4())

            # Use our own device_id generation since the client doesn't have it
            self.client.device_id = self.generate_device_id()

            # Login with verification challenge handling
            login_success = self.client.login(
                self.username,
                self.password,
                verification_code=self.get_verification_code
            )

            if login_success:
                logging.info("Login successful")
                # Save session for future use
                self.client.dump_settings(self.session_file)
                return True
            else:
                logging.error("Login failed")
                return False

        except Exception as e:
            logging.error(f"Login error: {e}")
            if self.debug:
                import traceback
                logging.error(traceback.format_exc())
            return False

    def get_verification_code(self):
        """Handle verification code requests"""
        # For automated systems, we'll skip verification for now
        # In a real implementation, this could be handled through UI
        logging.warning("Verification code requested - skipping for automated operation")
        return ""

    def upload_post(self, media_path, caption="", thumbnail_path=None):
        """Upload media to Instagram with anti-detection measures"""
        if not self.client:
            if not self.login():
                return None

        try:
            # Validate media file
            if not os.path.exists(media_path):
                logging.error(f"Media file not found: {media_path}")
                return None

            # Add human-like delay before upload
            self.human_delay(3, 10)

            # Determine media type and upload accordingly
            media_path = Path(media_path)
            media_type = media_path.suffix.lower()

            # Adjust caption with random line breaks to appear more human
            if caption:
                if random.random() > 0.7:  # 30% chance to add line break
                    parts = caption.split()
                    if len(parts) > 5:
                        idx = random.randint(3, len(parts) - 3)
                        caption = " ".join(parts[:idx]) + "\n" + " ".join(parts[idx:])

            # Upload based on media type
            result = None
            if media_type in ('.mp4', '.mov'):
                logging.info(f"Uploading video: {media_path}")

                # For reels/clips
                if thumbnail_path and os.path.exists(thumbnail_path):
                    result = self.client.clip_upload(
                        str(media_path),
                        caption=caption,
                        thumbnail=thumbnail_path
                    )
                else:
                    result = self.client.clip_upload(
                        str(media_path),
                        caption=caption
                    )
            elif media_type in ('.jpg', '.jpeg', '.png'):
                logging.info(f"Uploading photo: {media_path}")
                result = self.client.photo_upload(
                    str(media_path),
                    caption=caption
                )
            else:
                logging.error(f"Unsupported media type: {media_type}")
                return None

            # Get media ID
            media_id = getattr(result, 'id', getattr(result, 'pk', str(result)))
            logging.info(f"Upload successful! Media ID: {media_id}")

            # Increment upload count for rate limiting
            self.upload_count += 1

            # Add longer delay after upload to avoid rate limits
            self.human_delay(10, 20)

            # Periodically save session
            if self.upload_count % 3 == 0:
                self.client.dump_settings(self.session_file)

            return media_id

        except Exception as e:
            logging.error(f"Upload error: {e}")
            if self.debug:
                import traceback
                logging.error(traceback.format_exc())

            # If session error, try to re-login
            if "login" in str(e).lower() or "session" in str(e).lower():
                logging.info("Session may be invalid, attempting to re-login")
                if os.path.exists(self.session_file):
                    os.remove(self.session_file)
                if self.login():
                    logging.info("Re-login successful, retrying upload")
                    # Recursive call with one retry
                    return self.upload_post(media_path, caption, thumbnail_path)

            return None


def make_thumbnail_cv2(video_path: str, thumb_path: str, t_sec: int = 2) -> None:
    """Create thumbnail from video using OpenCV"""
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise RuntimeError("Video açılamadı")
    fps = cap.get(cv2.CAP_PROP_FPS) or 24
    cap.set(cv2.CAP_PROP_POS_FRAMES, int(fps * t_sec))
    ok, frame = cap.read()
    if not ok:
        raise RuntimeError("Kare yakalanamadı")
    cv2.imwrite(thumb_path, frame)
    cap.release()


# Removed old get_instagram_client function - now using InstagramUploader class


def validate_video_for_instagram(video_path: str) -> tuple[bool, str]:
    """
    Validates video file for Instagram compatibility.
    Returns (is_valid, error_message)
    """
    try:
        if not os.path.exists(video_path):
            return False, "Video dosyası bulunamadı"

        # Check file size (Instagram limit is around 100MB)
        file_size = os.path.getsize(video_path)
        if file_size > 100 * 1024 * 1024:  # 100MB
            return False, f"Video çok büyük: {file_size / (1024*1024):.1f}MB (max 100MB)"

        # Check video properties using OpenCV
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return False, "Video açılamadı"

        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)

        cap.release()

        # Check resolution (Instagram prefers 1080x1920 for vertical, 1920x1080 for horizontal)
        if width < 320 or height < 320:
            return False, f"Çözünürlük çok düşük: {width}x{height} (min 320x320)"

        if width > 1920 or height > 1920:
            return False, f"Çözünürlük çok yüksek: {width}x{height} (max 1920x1920)"

        # Check FPS
        if fps > 60:
            return False, f"FPS çok yüksek: {fps} (max 60)"

        # Check duration
        if fps > 0 and frame_count > 0:
            duration = frame_count / fps
            if duration > 60:
                return False, f"Video çok uzun: {duration:.1f}s (max 60s)"
            if duration < 1:
                return False, f"Video çok kısa: {duration:.1f}s (min 1s)"

        return True, "Video uygun"

    except Exception as e:
        return False, f"Video doğrulama hatası: {e}"


def preprocess_video_for_instagram(video_path: str) -> str:
    """
    Preprocesses video for Instagram upload using FFmpeg to fix duration and format issues.
    Returns path to processed video or original path if processing fails.
    """
    try:
        import subprocess
        from download import download_ffmpeg

        # Ensure FFmpeg is available
        ffmpeg_dir = download_ffmpeg()
        if not ffmpeg_dir:
            logging.warning("FFmpeg bulunamadı, video ön işleme atlanıyor")
            return video_path

        ffmpeg_exe = os.path.join(ffmpeg_dir, "ffmpeg.exe")
        if not os.path.exists(ffmpeg_exe):
            logging.warning("FFmpeg executable bulunamadı, video ön işleme atlanıyor")
            return video_path

        # Create processed video path
        base_name = os.path.splitext(video_path)[0]
        processed_path = f"{base_name}_processed.mp4"

        # Skip if already processed
        if os.path.exists(processed_path):
            logging.info(f"İşlenmiş video zaten mevcut: {processed_path}")
            return processed_path

        # FFmpeg command to fix video for Instagram
        # - Re-encode with H.264 codec
        # - Fix duration metadata
        # - Ensure compatible format
        # - Limit to 60 seconds max
        cmd = [
            ffmpeg_exe,
            "-i", video_path,
            "-c:v", "libx264",           # H.264 video codec
            "-c:a", "aac",               # AAC audio codec
            "-preset", "fast",           # Fast encoding
            "-crf", "23",                # Good quality
            "-movflags", "+faststart",   # Web optimization
            "-t", "60",                  # Limit to 60 seconds
            "-avoid_negative_ts", "make_zero",  # Fix timestamp issues
            "-fflags", "+genpts",        # Generate presentation timestamps
            "-y",                        # Overwrite output
            processed_path
        ]

        logging.info(f"Video Instagram için işleniyor: {video_path}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

        if result.returncode == 0 and os.path.exists(processed_path):
            # Verify processed video is valid
            duration = dummy_duration(processed_path)
            if duration > 0:
                logging.info(f"Video başarıyla işlendi: {processed_path} (süre: {duration:.1f}s)")
                return processed_path
            else:
                logging.warning("İşlenmiş video geçersiz, orijinal kullanılacak")
                if os.path.exists(processed_path):
                    os.remove(processed_path)
                return video_path
        else:
            logging.warning(f"Video işleme başarısız: {result.stderr}")
            if os.path.exists(processed_path):
                os.remove(processed_path)
            return video_path

    except Exception as e:
        logging.warning(f"Video ön işleme hatası: {e}")
        return video_path


def upload_instagram_item_new(username: str, password: str, video_path: str, thumbnail_path: str = None, caption: str = "") -> bool:
    """
    New Instagram upload implementation using InstagramUploader class with anti-detection measures.
    Integrates with SorcerioModules while maintaining video preprocessing and validation.
    """
    try:
        # Validate video file
        if not os.path.exists(video_path):
            logging.error(f"Video bulunamadı: {video_path}")
            return False

        # Initial validation
        is_valid, validation_msg = validate_video_for_instagram(video_path)
        if not is_valid:
            logging.warning(f"Video doğrulama uyarısı: {validation_msg}")
            # Continue with preprocessing to try to fix issues

        # Preprocess video for Instagram compatibility
        processed_video_path = preprocess_video_for_instagram(video_path)

        # Validate processed video
        is_valid_processed, validation_msg_processed = validate_video_for_instagram(processed_video_path)
        if not is_valid_processed:
            logging.error(f"İşlenmiş video geçersiz: {validation_msg_processed}")
            # Try with original video if processed version is invalid
            if processed_video_path != video_path:
                processed_video_path = video_path

        # Check video duration using processed video
        duration = dummy_duration(processed_video_path)
        if duration < 1:
            logging.error("Video çok kısa (1 saniyeden az).")
            return False
        if duration > 60:
            logging.warning("Video 60 saniyeden uzun; Instagram tarafından reddedilebilir.")

        # Generate thumbnail if not provided or doesn't exist
        if not thumbnail_path or not os.path.exists(thumbnail_path):
            try:
                thumbnail_path = os.path.splitext(processed_video_path)[0] + ".jpg"
                make_thumbnail_cv2(processed_video_path, thumbnail_path, t_sec=2)
                logging.info(f"Thumbnail üretildi: {thumbnail_path}")
            except Exception as e:
                logging.warning(f"Thumbnail üretilemedi ({e}); Instagram varsayılan kare kullanılacak")
                thumbnail_path = None

        # Log video information for debugging
        logging.info(f"Video bilgileri - Dosya: {os.path.basename(processed_video_path)}, Süre: {duration:.1f}s")

        # Create Instagram uploader with anti-detection measures
        uploader = InstagramUploader(username, password, debug=True)

        # Login to Instagram
        if not uploader.login():
            logging.error("Instagram login başarısız")
            return False

        logging.info("Instagram oturumu hazır.")

        # Upload using the new system
        media_id = uploader.upload_post(
            media_path=processed_video_path,
            caption=caption,
            thumbnail_path=thumbnail_path
        )

        # Clean up processed video if it's different from original
        if processed_video_path != video_path and os.path.exists(processed_video_path):
            try:
                os.remove(processed_video_path)
                logging.debug(f"İşlenmiş video temizlendi: {processed_video_path}")
            except:
                pass

        if media_id:
            logging.info(f"✔ Instagram'a yüklendi – Media ID: {media_id}")
            return True
        else:
            logging.error("Instagram upload başarısız")
            return False

    except Exception as e:
        logging.error(f"Instagram upload_instagram_item_new hatası: {e}")
        return False
