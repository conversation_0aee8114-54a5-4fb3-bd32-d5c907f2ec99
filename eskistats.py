import base64
import json
import logging
import os
import queue
import threading
import time
from collections import deque
from datetime import datetime
from pathlib import Path

import requests
from instagrapi import Client
from instagrapi.exceptions import LoginRequired


class StatisticsSystem:
    def __init__(self):
        self.stats_queue = queue.Queue()  # İstatistik çekme işleri için kuyruk
        self.profiles_to_process = set()  # İşlenecek profillerin kümesi
        self.stats_data = {}  # Her profilin istatistik verilerini saklayan sözlük
        self.stats_display_queue = deque()  # Ekranda gösterilecek istatistiklerin sırası
        self.last_profile_check = datetime.now()  # Son profil kontrol zamanı
        self.last_stats_update = {}  # Her profilin son istatistik güncelleme zamanı
        self.current_displayed_profile = None  # Şu an gösterilen profil
        self.last_display_change = datetime.now()  # Son gösterim değişikliği zamanı
        self.is_processing = False  # İstatistik çekme işlemi yapılıyor mu?
        self.worker_thread = None  # İstatistik çekme thread'i
        self.initialized = False  # Sistem başlatıldı mı?

    def istatistikleri_kaydet(self):
        """
        Tüm istatistik verilerini ve güncelleme zamanlarını dosyaya kaydeder.
        """
        try:
            stats_file = PROJECT_ROOT / "configuration" / "istatistikler.json"
            stats_file.parent.mkdir(parents=True, exist_ok=True)  # Ensure directory exists
            with open(stats_file, "w", encoding="utf-8") as f:
                json.dump({
                    "stats_data": self.stats_data,
                    "last_stats_update": {k: v.strftime("%Y-%m-%d %H:%M:%S") for k, v in self.last_stats_update.items()}
                }, f, indent=4, ensure_ascii=False)
        except Exception as e:
            logging.error(f"İstatistikler kaydedilemedi: {str(e)}")

    def istatistikleri_yukle(self):
        """
        Daha önce kaydedilmiş istatistik verilerini ve güncelleme zamanlarını dosyadan yükler.
        """
        try:
            stats_file = PROJECT_ROOT / "configuration" / "istatistikler.json"
            if not stats_file.exists():
                return
            with open(stats_file, "r", encoding="utf-8") as f:
                data = json.load(f)
                self.stats_data = data.get("stats_data", {})
                # last_stats_update string olarak kaydedildiği için tekrar datetime'a çeviriyoruz
                self.last_stats_update = {k: datetime.strptime(v, "%Y-%m-%d %H:%M:%S") for k, v in
                                          data.get("last_stats_update", {}).items()}
        except Exception as e:
            logging.error(f"İstatistikler yüklenemedi: {str(e)}")

    def profil_istatistiklerini_sil(self, profile_key):
        """
        Verilen profile_key'e ait tüm istatistik verilerini ve güncelleme zamanını sistemden ve dosyadan siler.
        Ayrıca istatistik deckindeki gösterimini anında sonlandırır.
        """
        try:
            if profile_key in self.stats_data:
                del self.stats_data[profile_key]
            if profile_key in self.last_stats_update:
                del self.last_stats_update[profile_key]
            if profile_key in self.stats_display_queue:
                self.stats_display_queue.remove(profile_key)

            # Eğer silinen profil şu an gösterilen profilse, başka bir profile geç
            if self.current_displayed_profile == profile_key:
                self.current_displayed_profile = next(iter(self.stats_display_queue), None)

            # İstatistikleri kaydet
            self.istatistikleri_kaydet()

            # Arayüzü hemen güncelle (her zaman UI thread'inde ve asenkron)
            try:
                from PyQt5.QtCore import QTimer
                from PyQt5.QtWidgets import QApplication
                for widget in QApplication.topLevelWidgets():
                    if hasattr(widget, "update_stats"):
                        QTimer.singleShot(0, widget.update_stats)
                        break
            except Exception as e:
                logging.error(f"Profil silindikten sonra UI güncellenirken hata: {str(e)}")

            logging.info(f"{profile_key} için istatistik verileri tamamen silindi.")
        except Exception as e:
            logging.error(f"{profile_key} için istatistik silinirken hata: {str(e)}")


stats_system = StatisticsSystem()

PROJECT_ROOT = Path(__file__).resolve().parent


def send_telegram_message(bot_token, chat_id, message_text):
    """
    Girilen mesajı Telegram üzerinden belirtilen chat_id'ye gönderir.
    """
    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
    payload = {
        "chat_id": chat_id,
        "text": message_text
    }
    try:
        requests.post(url, data=payload)
        logging.info("DESTEK mesajı Telegram'a gönderildi.")
    except Exception as e:
        logging.error(f"Telegram mesajı gönderilemedi: {str(e)}")


def format_stats_html(stats_data):
    """
    Verilen stats_data sözlüğüne göre, grid halinde 3 sütunlu HTML kutucukları üretir.
    """
    platform = stats_data.get("platform", "platform")
    username = stats_data.get("username", "kullanıcı")
    timestamp = stats_data.get("timestamp", "")
    metrics = stats_data.get("metrics", {})

    html = f"""
    <div style="padding:8px; background-color:#1E1E1E; font-family: 'Segoe UI', sans-serif;">
        <div style="font-size:13px; font-weight:bold; color:#FFFFFF;">{platform.capitalize()} İstatistikleri</div>
        <div style="font-size:11px; color:#FFFFFF;">@{username}</div>
        <div style="font-size:11px; color:#AAAAAA;">Güncelleme: {timestamp}</div>
        <div style="margin-top:8px; font-size:12px; font-weight:bold; color:#FFFFFF;">Son 7 Günün İstatistiği</div>
        <div style="display:grid; grid-template-columns: repeat(3, 1fr); gap:6px; margin-top:8px;">
    """

    for metric_name, metric_data in metrics.items():
        if isinstance(metric_data, dict):
            val = metric_data.get("value", "N/A")
            trend = metric_data.get("trend", "")
        else:
            val = metric_data
            trend = ""

        if "^" in trend:
            trend_color = "#4CAF50"
        elif "v" in trend:
            trend_color = "#F44336"
        else:
            trend_color = "#AAAAAA"

        html += f"""
            <div style="background-color:#2A2A2A; border:1px solid #373737; border-radius:6px;
                        min-height:45px; display:flex; align-items:center; justify-content:center;
                        font-size:11px; font-weight:bold; color:#FFFFFF;">
                {metric_name}
            </div>
            <div style="background-color:#2A2A2A; border:1px solid #373737; border-radius:6px;
                        min-height:45px; display:flex; align-items:center; justify-content:center;
                        font-size:12px; font-weight:bold; color:#FFFFFF;">
                {val}
            </div>
            <div style="background-color:#2A2A2A; border:1px solid #373737; border-radius:6px;
                        min-height:45px; display:flex; align-items:center; justify-content:center;
                        font-size:12px; font-weight:bold; color:{trend_color};">
                {trend}
            </div>
        """
    html += "</div></div>"
    return html


def format_instagram_stats_as_two_columns(stats_data):
    """
    Instagram istatistiklerini alt alta (tek sütun) kutucuklar halinde düzenler.
    Profil adının yanına Instagram logosu eklenir, diğer yapılar değişmeden korunur.
    """
    if not stats_data or "metrics" not in stats_data:
        return "<div style='color:#FFFFFF; font-family:Segoe UI;'>İstatistik verisi bulunamadı</div>"

    username = stats_data.get("username", "")
    metrics = stats_data.get("metrics", {})

    try:
        logo_path = PROJECT_ROOT / "instagram.png"
        with open(logo_path, "rb") as img_f:
            b64_logo = base64.b64encode(img_f.read()).decode()
        logo_html = f'<img src="data:image/png;base64,{b64_logo}" style="height:36px; width:auto; vertical-align:middle; margin-right:6px;" />'
    except Exception:
        logo_html = '<div style="width:36px; height:36px; margin-right:6px;"></div>'

    base_box_style = (
        "background-color:#2A2A2A;"
        "border:1px solid #373737;"
        "border-radius:6px;"
        "padding:10px;"
        "font-size:12px;"
        "color:#FFFFFF;"
        "margin-bottom:8px;"
        "display:flex;"
        "flex-direction:column;"
        "align-items:center;"
        "text-align:center;"
    )

    html = f'''
    <div style="font-family:Segoe UI; background-color:#1E1E1E; padding:10px;">
      <div style="display:flex; align-items:center; gap:6px; font-size:16px; font-weight:bold; color:#FFFFFF; margin-bottom:15px; letter-spacing:0.5px;">
        {logo_html}
        <div style="font-size:16px; font-weight:bold; color:#FFFFFF;">{username}</div>
      </div>
      <div style="display:flex; flex-direction:column;">
    '''

    followers = metrics.get("Takipçi Sayısı", {})
    html += f'''
      <div style="{base_box_style}">
        <div style="font-size:15px; font-weight:600; color:#DDDDDD; margin-bottom:6px;">Takipçi Sayısı</div>
        <div style="font-size:22px; font-weight:700;">{followers.get("value", "N/A")}</div>
      </div>
    '''

    for metric_name in ["Gösterimler", "Erişim", "Profil Ziyaretleri"]:
        metric_data = metrics.get(metric_name, {})
        value = metric_data.get("value", "N/A")
        trend = metric_data.get("trend", "")
        delta = metric_data.get("delta", 0)

        if delta > 0:
            trend_color = "#4CAF50"
        elif delta < 0:
            trend_color = "#F44336"
        else:
            trend_color = "#FFFFFF"
            trend = "0"

        html += f'''
      <div style="{base_box_style}">
        <div style="font-size:15px; font-weight:600; color:#DDDDDD; margin-bottom:6px;">{metric_name}</div>
        <div style="font-size:22px; font-weight:700;">{value}</div>
        <div style="font-size:14px; font-weight:bold; color:{trend_color}; margin-top:4px;">{trend}</div>
      </div>
    '''

    metric_data = metrics.get("Takipçi Değişimi", {})
    delta = metric_data.get("delta", 0)
    trend = metric_data.get("trend", "")

    if delta > 0:
        trend_color = "#4CAF50"
    elif delta < 0:
        trend_color = "#F44336"
    else:
        trend_color = "#FFFFFF"
        trend = "0"

    html += f'''
      <div style="{base_box_style}">
        <div style="font-size:15px; font-weight:600; color:#DDDDDD; margin-bottom:6px;">Takipçi Değişimi</div>
        <div style="font-size:22px; font-weight:700; color:{trend_color};">{trend}</div>
      </div>
    '''

    html += f'''
      </div>
      <div style="font-size:11px; color:#AAAAAA; text-align:right; margin-top:10px;">
        Son güncelleme: {stats_data.get("timestamp", "")}
      </div>
    </div>
    '''

    return html


def extract_instagram_metrics(username, password):
    """
    Instagram API ile istatistikleri çeker ve session yönetimini gelişmiş şekilde yapar.
    Session dosyası ile birlikte bir meta dosyası (<username>_session.meta.json) tutulur.
    - created: oluşturulma zamanı (timestamp)
    - last_used: son kullanım zamanı (timestamp)
    - next_refresh: bir sonraki zorunlu yenileme zamanı (timestamp)
    Session 48 saatten eskiyse, 48-72 veya 72-96 saat aralığında random bir yenileme zamanı atanır.
    Her kullanımda last_used güncellenir.
    """
    import json
    from datetime import datetime, timedelta
    import random
    import os
    import logging

    base_config_dir = PROJECT_ROOT / "configuration" / "instagram"
    session_dir = base_config_dir / "sessions"
    os.makedirs(session_dir, exist_ok=True)
    session_file = session_dir / f"{username}_session.json"
    meta_file = session_dir / f"{username}_session.meta.json"
    cl = Client()

    now = datetime.now()
    meta = {
        "created": None,
        "last_used": None,
        "next_refresh": None
    }
    # Meta dosyasını oku veya oluştur
    if os.path.exists(meta_file):
        try:
            with open(meta_file, "r", encoding="utf-8") as mf:
                meta = json.load(mf)
        except Exception:
            pass

    # Zamanları datetime objesine çevir
    def parse_time(val):
        if not val:
            return None
        try:
            return datetime.strptime(val, "%Y-%m-%d %H:%M:%S")
        except Exception:
            return None

    created = parse_time(meta.get("created"))
    last_used = parse_time(meta.get("last_used"))
    next_refresh = parse_time(meta.get("next_refresh"))

    session_valid = False
    session_needs_refresh = False
    session_exists = os.path.exists(session_file)

    if session_exists and created:
        age = (now - created).total_seconds() / 3600  # saat cinsinden
        # 48 saatten eskiyse, next_refresh atanmış mı bak
        if age >= 6:
            if not next_refresh or next_refresh < now:
                # 6-15 saat aralığında random bir zaman ata
                min_dt = created + timedelta(hours=6)
                max_dt = created + timedelta(hours=15)
                random_seconds = random.randint(0, int((max_dt - min_dt).total_seconds()))
                next_refresh = min_dt + timedelta(seconds=random_seconds)
                meta["next_refresh"] = next_refresh.strftime("%Y-%m-%d %H:%M:%S")
                # Meta dosyasını hemen güncelle
                with open(meta_file, "w", encoding="utf-8") as mf:
                    json.dump(meta, mf, indent=4, ensure_ascii=False)
            # Yenileme zamanı gelmiş mi?
            if next_refresh and now >= next_refresh:
                session_needs_refresh = True
            else:
                session_valid = True
        else:
            session_valid = True
    elif session_exists:
        # Meta yoksa veya bozuksa, session dosyasının dosya sisteminden oluşturulma zamanını kullan
        try:
            ctime = datetime.fromtimestamp(os.path.getctime(session_file))
            created = ctime
            meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
            meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
            meta["next_refresh"] = ""
            with open(meta_file, "w", encoding="utf-8") as mf:
                json.dump(meta, mf, indent=4, ensure_ascii=False)
            session_valid = True
        except Exception:
            session_valid = False

    try:
        logging.info(f"Instagram istatistikleri çekiliyor: {username}")
        if session_exists and session_valid and not session_needs_refresh:
            try:
                cl.load_settings(session_file)
                # Oturum test
                try:
                    logging.info(f"Oturum test ediliyor (metrics - {username}) [get_timeline_feed]...")
                    cl.get_timeline_feed()
                    logging.info(f"Oturum testi başarılı (metrics - {username}). Session kullanılacak.")
                except Exception as e_test:
                    logging.warning(
                        f"Oturum testi başarısız (metrics - {username}): {e_test}. Yeniden giriş yapılacak ve oturum güncellenecek.")
                    cl.login(username, password)
                    cl.dump_settings(session_file)
                    # Meta güncelle
                    created = now
                    meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
                    meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
                    meta["next_refresh"] = ""
                    with open(meta_file, "w", encoding="utf-8") as mf:
                        json.dump(meta, mf, indent=4, ensure_ascii=False)
                # Başarılıysa last_used güncelle
                meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
                with open(meta_file, "w", encoding="utf-8") as mf:
                    json.dump(meta, mf, indent=4, ensure_ascii=False)
            except Exception as e:
                logging.warning(f"Oturum yükleme veya kullanma hatası ({e}), normal giriş deneniyor.")
                cl.login(username, password)
                cl.dump_settings(session_file)
                # Meta güncelle
                created = now
                meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
                meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
                meta["next_refresh"] = ""
                with open(meta_file, "w", encoding="utf-8") as mf:
                    json.dump(meta, mf, indent=4, ensure_ascii=False)
        else:
            # Session yok veya yenileme zamanı gelmiş
            cl.login(username, password)
            cl.dump_settings(session_file)
            created = now
            meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
            meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
            meta["next_refresh"] = ""
            with open(meta_file, "w", encoding="utf-8") as mf:
                json.dump(meta, mf, indent=4, ensure_ascii=False)
        # API'dan veri çek
        try:
            insights = cl.insights_account()
            profile_info = cl.user_info_by_username(username)
            logging.info(f"API'dan data çekildi: {username}")
        except LoginRequired as lr_exc:
            logging.warning(
                f"Instagram API çağrısı LoginRequired hatası verdi ({lr_exc}). Session silinip yeniden giriş denenecek: {username}")
            if os.path.exists(session_file):
                try:
                    os.remove(session_file)
                    logging.info(f"Eski/sorunlu session dosyası silindi: {session_file}")
                except Exception as rm_err:
                    logging.error(f"Session dosyası ({session_file}) silinirken hata: {rm_err}")
            if os.path.exists(meta_file):
                try:
                    os.remove(meta_file)
                except Exception:
                    pass
            logging.info(f"Zorunlu yeniden giriş yapılıyor: {username}")
            cl.login(username, password)
            logging.info(f"Zorunlu yeniden giriş başarılı: {username}")
            cl.dump_settings(session_file)
            created = now
            meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
            meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
            meta["next_refresh"] = ""
            with open(meta_file, "w", encoding="utf-8") as mf:
                json.dump(meta, mf, indent=4, ensure_ascii=False)
            # Retry fetching data
            insights = cl.insights_account()
            profile_info = cl.user_info_by_username(username)
            logging.info(f"Zorunlu yeniden giriş sonrası data başarıyla çekildi: {username}")

        # None değerleri 0 ile değiştir
        def handle_none(value):
            return 0 if value is None else value

        followers_count = handle_none(profile_info.follower_count)
        impressions_count = handle_none(insights.get("account_insights_unit", {}).get("impressions_metric_count", 0))
        impressions_delta = handle_none(insights.get("account_insights_unit", {}).get("impressions_metric_delta", 0))
        reach_count = handle_none(insights.get("account_insights_unit", {}).get("reach_metric_count", 0))
        reach_delta = handle_none(insights.get("account_insights_unit", {}).get("reach_metric_delta", 0))
        profile_visits_count = handle_none(
            insights.get("account_insights_unit", {}).get("profile_visits_metric_count", 0))
        profile_visits_delta = handle_none(
            insights.get("account_insights_unit", {}).get("profile_visits_metric_delta", 0))
        followers_delta = handle_none(insights.get("followers_unit", {}).get("followers_delta_from_last_week", 0))
        metrics = {
            "Takipçi Sayısı": {
                "value": followers_count,
                "trend": ""
            },
            "Gösterimler": {
                "value": impressions_count,
                "trend": f"{impressions_delta:+d}",
                "delta": impressions_delta
            },
            "Erişim": {
                "value": reach_count,
                "trend": f"{reach_delta:+d}",
                "delta": reach_delta
            },
            "Profil Ziyaretleri": {
                "value": profile_visits_count,
                "trend": f"{profile_visits_delta:+d}",
                "delta": profile_visits_delta
            },
            "Takipçi Değişimi": {
                "value": "",
                "trend": f"{followers_delta:+d}",
                "delta": followers_delta
            }
        }
        return {
            "platform": "instagram",
            "username": username,
            "timestamp": now.strftime("%Y-%m-%d %H:%M:%S"),
            "metrics": metrics
        }
    except LoginRequired as final_lr_exc:
        logging.error(
            f"Instagram istatistik çekme hatası: LoginRequired (yeniden deneme sonrası da devam ediyor) ({username}): {final_lr_exc}")
        if os.path.exists(session_file):
            try:
                os.remove(session_file)
                logging.info(
                    f"Başarısız (LoginRequired) yeniden deneme sonrası session dosyası silindi: {session_file}")
            except Exception as rm_err:
                logging.error(f"Session dosyası ({session_file}) silinirken (LoginRequired sonrası) hata: {rm_err}")
        if os.path.exists(meta_file):
            try:
                os.remove(meta_file)
            except Exception:
                pass
        return None
    except Exception as e:
        logging.error(f"Genel Instagram istatistik çekme hatası ({username}): {str(e)}")
        if "login" in str(e).lower() and os.path.exists(session_file):
            logging.warning(f"Genel hata 'login' içeriyor, session dosyası ({session_file}) siliniyor.")
            try:
                os.remove(session_file)
                logging.info(f"Genel 'login' hatası sonrası session dosyası silindi: {session_file}")
            except Exception as rm_s_err:
                logging.error(
                    f"Genel 'login' hatası sonrası session dosyası ({session_file}) silinirken hata: {rm_s_err}")
        if os.path.exists(meta_file):
            try:
                os.remove(meta_file)
            except Exception:
                pass
        return None


def extract_twitter_metrics(username, password):
    """
    Twitter istatistiklerini çeker ve metrikleri sözlük olarak döndürür.
    Twitter yazı ve ok işaretlerinden yüzde değişim değerlerini de ayıklar.
    Twitter oturum meta dosyası ile tazelik kontrolü eklenmiştir.
    """
    import undetected_chromedriver as uc
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    import time
    from datetime import datetime, timedelta
    import re
    import os
    import json
    import random
    import logging

    # --- Twitter session meta yönetimi ---
    base_config_dir = PROJECT_ROOT / "configuration" / "twitter"
    session_dir = base_config_dir / "sessions"
    os.makedirs(session_dir, exist_ok=True)
    meta_file = session_dir / f"{username}_session.meta.json"

    now = datetime.now()
    meta = {
        "created": None,
        "last_used": None,
        "next_refresh": None
    }
    # Meta dosyasını oku veya oluştur
    if os.path.exists(meta_file):
        try:
            with open(meta_file, "r", encoding="utf-8") as mf:
                meta = json.load(mf)
        except Exception:
            pass

    def parse_time(val):
        if not val:
            return None
        try:
            return datetime.strptime(val, "%Y-%m-%d %H:%M:%S")
        except Exception:
            return None

    created = parse_time(meta.get("created"))
    last_used = parse_time(meta.get("last_used"))
    next_refresh = parse_time(meta.get("next_refresh"))

    session_valid = False
    session_needs_refresh = False
    session_exists = True  # Twitter'da session dosyası yok, sadece meta ile kontrol

    if created:
        age = (now - created).total_seconds() / 3600  # saat cinsinden
        # 8 saatten eskiyse, next_refresh atanmış mı bak
        if age >= 8:
            if not next_refresh or next_refresh < now:
                # 8-12 saat aralığında random bir zaman ata
                min_dt = created + timedelta(hours=8)
                max_dt = created + timedelta(hours=12)
                random_seconds = random.randint(0, int((max_dt - min_dt).total_seconds()))
                next_refresh = min_dt + timedelta(seconds=random_seconds)
                meta["next_refresh"] = next_refresh.strftime("%Y-%m-%d %H:%M:%S")
                # Meta dosyasını hemen güncelle
                with open(meta_file, "w", encoding="utf-8") as mf:
                    json.dump(meta, mf, indent=4, ensure_ascii=False)
            # Yenileme zamanı gelmiş mi?
            if next_refresh and now >= next_refresh:
                session_needs_refresh = True
            else:
                session_valid = True
        else:
            session_valid = True
    else:
        # Meta yoksa veya bozuksa, ilk girişte oluştur
        created = now
        meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
        meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
        meta["next_refresh"] = ""
        with open(meta_file, "w", encoding="utf-8") as mf:
            json.dump(meta, mf, indent=4, ensure_ascii=False)
        session_valid = True

    # --- Twitter oturumunu yenileme ---
    if session_needs_refresh:
        # Oturum klasörünü sil (chrome_profile_<username>)
        base_project_dir = PROJECT_ROOT
        user_profile_dir_path = base_project_dir / f"chrome_profile_{username}"
        if os.path.exists(user_profile_dir_path):
            import shutil
            try:
                shutil.rmtree(user_profile_dir_path)
                logging.info(f"Twitter oturum klasörü silindi (tazeleme): {user_profile_dir_path}")
            except Exception as e:
                logging.warning(f"Twitter oturum klasörü silinemedi (tazeleme): {user_profile_dir_path} - {e}")
        # Meta güncelle
        created = now
        meta["created"] = created.strftime("%Y-%m-%d %H:%M:%S")
        meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
        meta["next_refresh"] = ""
        with open(meta_file, "w", encoding="utf-8") as mf:
            json.dump(meta, mf, indent=4, ensure_ascii=False)

    # --- Kalan orijinal kod ---
    def setup_driver():
        options = uc.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--lang=en-US')
        options.add_argument('--force-language=en')
        options.add_experimental_option('prefs', {
            'intl.accept_languages': 'en-US,en',
        })
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--window-size=1920,1080')
        driver = uc.Chrome(options=options, version_main=135)
        return driver
    def login_twitter(driver, username, password):
        driver.get("https://twitter.com/login?lang=en")
        time.sleep(3)
        try:
            accept_cookies = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, '//span[contains(text(), "Accept all cookies")]'))
            )
            accept_cookies.click()
            time.sleep(2)
        except:
            pass
        username_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, 'input[autocomplete="username"]'))
        )
        username_input.send_keys(username)
        username_input.send_keys(Keys.RETURN)
        time.sleep(2)
        password_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, 'input[name="password"]'))
        )
        password_input.send_keys(password)
        password_input.send_keys(Keys.RETURN)
        time.sleep(5)
        return True
    def extract_full_text(driver):
        driver.get("https://x.com/i/account_analytics")
        time.sleep(5)
        WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        full_text = driver.find_element(By.TAG_NAME, "body").text
        return full_text
    def parse_metrics(full_text, metric_labels):
        lines = full_text.splitlines()
        metrics = {}
        for label in metric_labels:
            indices = [i for i, line in enumerate(lines) if line.strip() == label]
            if indices:
                i = indices[-1]
                main_value = lines[i + 1] if i + 1 < len(lines) else "N/A"
                arrow = ""
                trend = ""
                for j in range(i + 2, len(lines)):
                    if lines[j] in ["↑", "↓", "^", "v"]:
                        arrow = lines[j]
                        trend = lines[j + 1] if j + 1 < len(lines) else ""
                        break
                percent_value = ""
                if trend:
                    percent_match = re.search(r'(-?\d+(\.\d+)?)%', trend)
                    if percent_match:
                        percent_value = percent_match.group(1) + "%"
                if arrow and trend:
                    metrics[label] = {
                        "value": main_value,
                        "trend": f"{arrow} {trend}",
                        "percent": percent_value,
                        "direction": "up" if arrow in ["↑", "^"] else "down"
                    }
                else:
                    metrics[label] = {
                        "value": main_value,
                        "trend": "",
                        "percent": "",
                        "direction": ""
                    }
        return metrics
    def click_4w_button(driver):
        try:
            button_4w = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, '//button[.//div[text()="4W"]]'))
            )
            button_4w.click()
            time.sleep(5)
            return True
        except Exception:
            return False
    driver = setup_driver()
    try:
        if not login_twitter(driver, username, password):
            return None
        full_text_7d = extract_full_text(driver)
        is_premium = full_text_7d and "Unlock Analytics with X Premium" not in full_text_7d
        if is_premium:
            metric_labels = [
                "Impressions", "Engagement rate", "Engagements", "Profile visits",
                "Replies", "Likes", "Reposts", "Bookmarks", "Shares"
            ]
        else:
            metric_labels = [
                "Impressions", "Likes", "Profile visits", "New follows", "Replies", "Reposts"
            ]
        metrics_7d = parse_metrics(full_text_7d, metric_labels)
        metrics = metrics_7d
        if is_premium and click_4w_button(driver):
            full_text_4w = extract_full_text(driver)
            metrics_4w = parse_metrics(full_text_4w, metric_labels)
            metrics["4W"] = metrics_4w
        # --- Twitter meta güncelle (her çekimde last_used güncellenir) ---
        meta["last_used"] = now.strftime("%Y-%m-%d %H:%M:%S")
        with open(meta_file, "w", encoding="utf-8") as mf:
            json.dump(meta, mf, indent=4, ensure_ascii=False)
        return {
            "platform": "twitter",
            "username": username,
            "timestamp": now.strftime("%Y-%m-%d %H:%M:%S"),
            "is_premium": is_premium,
            "metrics": metrics
        }
    except Exception as e:
        import logging
        logging.error(f"Twitter istatistik çekme hatası: {str(e)}")
        return None
    finally:
        driver.quit()


def format_instagram_stats_for_display(stats: dict) -> str:
    """
    Instagram istatistik verilerini 2'li kutu formatında HTML döndürür.
    Twitter formatıyla birebir aynı yapıdadır. Değişim yüzdesi içermez.
    """
    html = '<div class="stats-grid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 6px;">\n'

    for key, value in stats.items():
        label = key.replace('_', ' ').capitalize()
        html += f'''
        <div class="stat-box">
            <div class="stat-label">{label}</div>
        </div>
        <div class="stat-box">
            <div class="stat-value">{value}</div>
        </div>
        '''

    html += '\n</div>'
    return html


def format_twitter_stats_for_display(stats_data):
    """
    Twitter istatistiklerini görüntülemek için formatlar
    """
    if not stats_data or "metrics" not in stats_data:
        return "İstatistik verisi bulunamadı."

    metrics = stats_data["metrics"]
    username = stats_data["username"]
    timestamp = stats_data["timestamp"]
    is_premium = stats_data.get("is_premium", False)

    formatted = f"Twitter İstatistikleri: @{username}<br>"
    formatted += f"Son güncelleme: {timestamp}<br><br>"

    # 7D verileri
    formatted += "<strong>Son 7 Günün İstatistiği:</strong><br>"
    formatted += "=" * 40 + "<br>"

    # Tüm metrikleri sıralı şekilde ekleyelim
    priority_metrics = ["Impressions", "Engagement rate", "Engagements", "Profile visits",
                        "Replies", "Likes", "Reposts", "Bookmarks", "Shares"]

    for label in priority_metrics:
        if label in metrics:
            metric_data = metrics[label]
            value = metric_data.get("value", "N/A")
            trend = metric_data.get("trend", "")
            formatted += f"{label:15s}: {value}"
            if trend:
                formatted += f", change: {trend}"
            formatted += "<br>"

    formatted += "=" * 40 + "<br><br>"

    # 4W verileri (premium ise)
    if is_premium and "4W" in metrics:
        formatted += "<strong>Son 4 Hafta İstatistiği:</strong><br>"
        formatted += "=" * 40 + "<br>"

        # 4W için de aynı metrikleri gösteriyoruz
        for label in priority_metrics:
            if label in metrics["4W"]:
                metric_data = metrics["4W"][label]
                value = metric_data.get("value", "N/A")
                trend = metric_data.get("trend", "")
                formatted += f"{label:15s}: {value}"
                if trend:
                    formatted += f", change: {trend}"
                formatted += "<br>"

        formatted += "=" * 40 + "<br>"

    return formatted


def format_twitter_stats_as_boxes(stats_data):
    username = stats_data.get("username", "kullanıcı")
    timestamp = stats_data.get("timestamp", "")
    metrics = stats_data.get("metrics", {})

    base_box_style = (
        "background-color:#2A2A2A;"
        "border:1px solid #373737;"
        "border-radius:6px;"
        "padding:10px;"
        "font-size:12px;"
        "color:#FFFFFF;"
        "text-align:center;"
        "display:flex;"
        "flex-direction:column;"
        "align-items:center;"
        "min-height:60px;"
    )

    html = f'<div style="font-family:Segoe UI; background-color:#1E1E1E; padding:8px;">'
    try:
        with open(PROJECT_ROOT / "x.png", "rb") as img_f:
            b64_logo = base64.b64encode(img_f.read()).decode()
        twitter_logo_html = (
            f'<img src="data:image/png;base64,{b64_logo}" '
            f'style="height:36px; width:auto; vertical-align:middle; margin-right:6px;" />'
        )
    except:
        twitter_logo_html = ""

    html += (
        f'<div style="display:flex; align-items:center; gap:6px; '
        f'font-size:16px; font-weight:bold; color:#FFFFFF; margin-bottom:10px; '
        f'letter-spacing:0.5px;">'
        f'{twitter_logo_html}'
        f'<span style="font-size:16px; letter-spacing:0.6px; margin-left:-1px;">{username}</span>'
        f'</div>'
    )

    def render_metric_stack(title, metric_data):
        section = (
            f'<div style="margin-top:12px; font-size:13px; '
            f'font-weight:bold; color:#FFFFFF; text-align:center;">{title}</div>'
        )
        section += '<div style="display:flex; flex-direction:column; gap:4px; margin-top:6px;">'
        for label, values in metric_data.items():
            if not isinstance(values, dict):
                continue
            value = values.get("value", "N/A")
            percent = values.get("percent", "")
            direction = values.get("direction", "")

            trend_color = (
                "#4CAF50" if direction == "up"
                else "#F44336" if direction == "down"
                else "#AAAAAA"
            )
            percent_html = (
                f'<div style="margin-top:6px; color:{trend_color}; '
                f'font-weight:bold; font-size:14px; text-align:center;">'
                f'{percent}'
                f'</div>'
            ) if percent else ""

            section += (
                f'<div style="{base_box_style}">'
                f'<div style="font-size:15px; font-weight:600; color:#DDDDDD; '
                f'margin-bottom:4px; text-align:center;">{label}</div>'
                f'<div style="font-size:22px; font-weight:700; color:#FFFFFF; '
                f'text-align:center;">{value}</div>'
                f'{percent_html}'
                f'</div>'
            )
        section += '</div>'
        return section

    metrics_7d = {k: v for k, v in metrics.items() if k != "4W"}
    if metrics_7d:
        html += render_metric_stack("Son 7 Günün İstatistiği", metrics_7d)

    if "4W" in metrics:
        html += render_metric_stack("Son 4 Haftanın İstatistiği", metrics["4W"])

    html += "</div>"
    return html


def stats_worker():
    """
    Arka planda çalışan, istatistik çekme işlemlerini gerçekleştiren worker thread
    """
    global stats_system
    while True:
        try:
            profile_data = stats_system.stats_queue.get(timeout=1)
            stats_system.is_processing = True
            platform = profile_data["platform"]
            username = profile_data["username"]
            password = profile_data["password"]
            logging.info(f"{platform.capitalize()} istatistikleri çekiliyor: {username}")

            if platform == "instagram":
                stats = extract_instagram_metrics(username, password)
            elif platform == "twitter":
                stats = extract_twitter_metrics(username, password)
            else:
                stats = None
                logging.error(f"Bilinmeyen platform: {platform}")

            if stats:
                profile_key = f"{platform}_{username}"
                stats_system.stats_data[profile_key] = stats
                stats_system.last_stats_update[profile_key] = datetime.now()
                if profile_key not in stats_system.stats_display_queue:
                    stats_system.stats_display_queue.append(profile_key)
                logging.info(f"{platform.capitalize()} istatistikleri başarıyla güncellendi: {username}")
                # Her yeni veri geldiğinde istatistikleri dosyaya kaydet
                stats_system.istatistikleri_kaydet()

            stats_system.stats_queue.task_done()
            stats_system.profiles_to_process.discard(f"{platform}_{username}")

        except queue.Empty:
            stats_system.is_processing = False
            time.sleep(1)
        except Exception as e:
            logging.error(f"İstatistik worker'da hata: {str(e)}")
            stats_system.is_processing = False
            time.sleep(5)


def check_profiles(force: bool = False):
    """
    Profilleri ve medya dosyalarını denetler.
    Videosu olmayan thumbnail'ler artık temizlenir.
    """
    global stats_system
    try:
        now = datetime.now()
        # force True ise zaman kontrolü atlanır, her durumda çalışır
        is_startup = (now - stats_system.last_profile_check).total_seconds() > 3600
        regular_check = (now - stats_system.last_profile_check).total_seconds() >= 120

        if not force and not is_startup and not regular_check:
            return

        logging.info("Profiller kontrol ediliyor...")
        stats_system.last_profile_check = now

        config_dir = PROJECT_ROOT / "configuration"
        base_dir = PROJECT_ROOT
        videos_dir = base_dir / "videos"

        # Medya klasörlerini kontrol et
        instagram_output_dir = videos_dir / "instagramdownloaded"
        twitter_output_dir = videos_dir / "twitterdownloaded"
        youtube_output_dir = videos_dir / "youtubedownloaded"

        # Tüm medya klasörlerinin var olduğundan emin ol
        os.makedirs(instagram_output_dir, exist_ok=True)
        os.makedirs(twitter_output_dir, exist_ok=True)
        os.makedirs(youtube_output_dir, exist_ok=True)

        # Tüm medya dosyalarını topla
        all_media_files = {}
        for output_dir in [instagram_output_dir, twitter_output_dir, youtube_output_dir]:
            if os.path.exists(output_dir):
                for file in os.listdir(output_dir):
                    file_path = os.path.join(output_dir, file)
                    if os.path.isfile(file_path):
                        all_media_files[file_path] = False  # False = henüz bir JSON'da bulunamadı

        # Mevcut profilleri takip etmek için set kullan
        mevcut_profiller = set()

        # Tüm JSON'ları işle ve URL'leri de takip et (URL normalizasyonu için)
        url_to_file_map = {}  # URL -> file_path eşleşmeleri

        platforms = ["instagram", "twitter"]
        for platform in platforms:
            platform_dir = config_dir / platform
            if not platform_dir.exists():
                continue
            profile_files = [f for f in os.listdir(platform_dir) if f.endswith(".json")]
            for profile_file in profile_files:
                profile_path = platform_dir / profile_file
                try:
                    # Profil dosyasını oku, bozuksa varsayılan oluştur
                    with open(profile_path, "r", encoding="utf-8") as f:
                        try:
                            profile_data = json.load(f)
                        except json.JSONDecodeError:
                            logging.warning(f"Bozuk JSON: {profile_path}. Yeni, varsayılan profil oluşturuluyor.")
                            profile_data = {
                                "username": "",
                                "password": "",
                                "schedule": {
                                    "monday": [], "tuesday": [], "wednesday": [], "thursday": [],
                                    "friday": [], "saturday": [], "sunday": []
                                },
                                "hashtags": [],
                                "links": [],
                                "downloaded": []
                            }
                            with open(profile_path, "w", encoding="utf-8") as fw:
                                json.dump(profile_data, fw, indent=4, ensure_ascii=False)

                    # Temizlik: Silinmiş dosyaları ve duplicate kayıtları ayıkla
                    original_downloaded = profile_data.get("downloaded", [])
                    seen_paths = set()
                    seen_urls = set()
                    cleaned_downloaded = []

                    # URL normalizasyonu için kullanılacak
                    def normalize_url(url):
                        if url:
                            return url.split("?")[0].strip()
                        return ""

                    # JSON'daki medya dosyalarını kontrol et
                    for item in original_downloaded:
                        fpath = item.get("file_path", "")
                        url = normalize_url(item.get("url", ""))
                        caption = item.get("caption", "")

                        # Fiziksel dosya var mı kontrol et
                        if not os.path.exists(fpath):
                            logging.info(f"Fiziksel dosya bulunamadı, JSON'dan kaldırılıyor: {fpath}")
                            continue  # fiziksel dosya yoksa atla

                        # Duplicate kontrol
                        if fpath in seen_paths:
                            logging.info(f"Duplicate dosya yolu, JSON'dan kaldırılıyor: {fpath}")
                            continue  # duplicate dosya varsa atla

                        # URL duplicate kontrolü (normalize edilmiş)
                        if url in seen_urls:
                            # Aynı URL'ye sahip önceki bir kaydın dosya yolu ile karşılaştır
                            # Eğer URL ve dosya yolu farklı kayıtlarda varsa, bu duplicate olabilir
                            if url in url_to_file_map and url_to_file_map[url] != fpath:
                                logging.info(f"Aynı URL farklı dosyalarla kaydedilmiş: {url}")
                                # İki dosya da varsa, eski olanı sil
                                previous_path = url_to_file_map[url]
                                if os.path.exists(previous_path) and os.path.exists(fpath):
                                    # Daha yeni olanı koru
                                    prev_mtime = os.path.getmtime(previous_path)
                                    curr_mtime = os.path.getmtime(fpath)
                                    if curr_mtime > prev_mtime:
                                        # Mevcut dosya daha yeni, eskisini güvenli sil
                                        from utils import safe_file_delete, protect_media_files

                                        # Tüm profillerin korunan dosyalarını topla
                                        all_protected_files = set()
                                        for platform in ["instagram", "twitter"]:
                                            platform_dir = os.path.join(base_dir, platform)
                                            if os.path.exists(platform_dir):
                                                for profile_file in os.listdir(platform_dir):
                                                    if profile_file.endswith(".json"):
                                                        profile_json_path = os.path.join(platform_dir, profile_file)
                                                        protected = protect_media_files(profile_json_path)
                                                        all_protected_files.update(protected)

                                        if safe_file_delete(previous_path, all_protected_files):
                                            logging.info(f"Duplicate URL'nin eski dosyası güvenli bir şekilde silindi: {previous_path}")
                                            # Url-dosya haritasını güncelle
                                            url_to_file_map[url] = fpath
                                    else:
                                        # Önceki dosya daha yeni, mevcut dosyayı güvenli sil
                                        if safe_file_delete(fpath, all_protected_files):
                                            logging.info(f"Duplicate URL'nin yeni dosyası güvenli bir şekilde silindi: {fpath}")
                                            continue  # Mevcut item'ı listeye ekleme
                            continue  # duplicate URL varsa atla

                        # Dosya geçerliyse işaretle ve listeye ekle
                        seen_paths.add(fpath)
                        seen_urls.add(url)
                        cleaned_downloaded.append(item)

                        # Bu dosyayı JSON'da var olarak işaretle
                        if fpath in all_media_files:
                            all_media_files[fpath] = True

                        # URL'yi dosya ile eşleştir
                        if url:
                            url_to_file_map[url] = fpath

                    # Değişiklik varsa JSON'u güncelle
                    if len(cleaned_downloaded) < len(original_downloaded):
                        profile_data["downloaded"] = cleaned_downloaded
                        with open(profile_path, "w", encoding="utf-8") as f:
                            json.dump(profile_data, f, indent=4, ensure_ascii=False)
                        logging.info(f"{profile_file} için silinmiş veya duplicate medya kayıtları temizlendi.")

                    username = profile_data.get("username", "").strip()
                    password = profile_data.get("password", "").strip()
                    if username and password:
                        profile_key = f"{platform}_{username}"
                        # Mevcut profil listesine ekle
                        mevcut_profiller.add(profile_key)

                        if profile_key in stats_system.profiles_to_process:
                            continue  # Zaten işleniyor

                        last_update = stats_system.last_stats_update.get(profile_key)
                        # İstatistik varsa ve 24 saat içindeyse, mevcut veriyi kullan
                        if last_update and (now - last_update).total_seconds() < 86400:
                            # Profil zaten istatistik display queue'da yoksa ekle
                            if profile_key not in stats_system.stats_display_queue:
                                stats_system.stats_display_queue.append(profile_key)
                            continue

                        # 24 saatten eski veya hiç veri yoksa, hemen çek
                        stats_system.stats_queue.put({
                            "platform": platform,
                            "username": username,
                            "password": password
                        })
                        stats_system.profiles_to_process.add(profile_key)
                        logging.info(f"İstatistik çekme kuyruğuna eklendi: {platform}/{username}")
                except Exception as e:
                    logging.error(f"Profil dosyası okunurken hata ({profile_file}): {str(e)}")

        # JSON'da olmayan medya dosyalarını temizle
        # Thumbnail'ler için özel kontrol yapılacak
        for file_path, is_in_json in all_media_files.items():
            # .part (halen yazılan) dosyalar görmezden gelinir
            if file_path.lower().endswith(".part"):
                continue

            # Tüm profillerin korunan dosyalarını topla (güvenli silme için)
            from utils import safe_file_delete, protect_media_files
            all_protected_files = set()
            for platform in ["instagram", "twitter"]:
                platform_dir = os.path.join(base_dir, platform)
                if os.path.exists(platform_dir):
                    for profile_file in os.listdir(platform_dir):
                        if profile_file.endswith(".json"):
                            profile_json_path = os.path.join(platform_dir, profile_file)
                            protected = protect_media_files(profile_json_path)
                            all_protected_files.update(protected)

            # Thumbnail (.jpg) dosyaları için özel kontrol
            if file_path.lower().endswith(".jpg"):
                # Thumbnail'in ilişkili olduğu video dosyasını kontrol et
                video_path = os.path.splitext(file_path)[0] + ".mp4"
                # Eğer ilişkili video hala varsa veya thumbnail JSON'da kayıtlıysa, thumbnail'i koru
                if os.path.exists(video_path) or is_in_json:
                    continue
                else:
                    # Video yoksa ve thumbnail JSON'da değilse, güvenli sil
                    if safe_file_delete(file_path, all_protected_files):
                        logging.info(f"Videosu olmayan thumbnail güvenli bir şekilde silindi: {file_path}")
                    continue

            # Diğer medya dosyaları (video vb.) için normal kontrol
            if not is_in_json:
                if safe_file_delete(file_path, all_protected_files):
                    logging.info(f"JSON'da olmayan medya dosyası güvenli bir şekilde temizlendi: {file_path}")

        # Var olmayan profillerin istatistiklerini temizle
        silinecek_istatistikler = set()

        # stats_data içindeki kayıtları kontrol et
        for profile_key in list(stats_system.stats_data.keys()):
            if profile_key not in mevcut_profiller:
                silinecek_istatistikler.add(profile_key)

        # stats_display_queue içindeki kayıtları kontrol et
        for profile_key in list(stats_system.stats_display_queue):
            if profile_key not in mevcut_profiller:
                stats_system.stats_display_queue.remove(profile_key)
                silinecek_istatistikler.add(profile_key)

        # last_stats_update içindeki kayıtları kontrol et
        for profile_key in list(stats_system.last_stats_update.keys()):
            if profile_key not in mevcut_profiller:
                silinecek_istatistikler.add(profile_key)

        # Var olmayan profillerin istatistiklerini sil
        for profile_key in silinecek_istatistikler:
            if profile_key in stats_system.stats_data:
                del stats_system.stats_data[profile_key]
            if profile_key in stats_system.last_stats_update:
                del stats_system.last_stats_update[profile_key]
            if profile_key in stats_system.profiles_to_process:
                stats_system.profiles_to_process.discard(profile_key)
            logging.info(f"Var olmayan profile ait istatistik temizlendi: {profile_key}")

        # Eğer silinen istatistik varsa dosyayı güncelle
        if silinecek_istatistikler:
            stats_system.istatistikleri_kaydet()

            # Eğer şu anda gösterilen profil silindiyse, başka bir profile geç
            if stats_system.current_displayed_profile in silinecek_istatistikler:
                stats_system.current_displayed_profile = next(iter(stats_system.stats_display_queue), None)

    except Exception as e:
        logging.error(f"Profilleri kontrol ederken hata: {str(e)}")


def update_can_bars_ui():
    """
    Can barlarını anında güncellemek için kullanılan yardımcı fonksiyon.
    Bu fonksiyon, profil JSON dosyalarındaki "downloaded" bölümü değiştiğinde çağrılmalıdır.
    """
    try:
        # QApplication'a erişim için import
        from PyQt5.QtWidgets import QApplication

        # Ana pencereyi bul ve update_stats metodunu çağır
        for widget in QApplication.topLevelWidgets():
            if hasattr(widget, "update_stats"):
                widget.update_stats()
                logging.info("Can barları başarıyla güncellendi (anlık)")
                break
    except Exception as e:
        logging.error(f"Can barları anlık güncellenirken hata: {str(e)}")
        # Hata durumunda en azından HTML'i güncelle
        update_stats_display_html()

def update_stats_display_html() -> str:
    try:
        base_dir = PROJECT_ROOT / "configuration"
        all_profiles = []
        # Var olan profilleri takip etmek için set kullan
        mevcut_profiller = set()

        for platform in ["instagram", "twitter"]:
            platform_dir = base_dir / platform
            if not platform_dir.exists():
                continue
            for file in os.listdir(platform_dir):
                if file.endswith(".json"):
                    profile_path = platform_dir / file
                    try:
                        with open(profile_path, "r", encoding="utf-8") as f:
                            try:
                                data = json.load(f)
                            except json.JSONDecodeError:
                                logging.warning(f"Bozuk JSON: {profile_path}. Varsayılan yapı kaydediliyor.")
                                data = {
                                    "username": "",
                                    "password": "",
                                    "schedule": {
                                        "monday": [], "tuesday": [], "wednesday": [], "thursday": [],
                                        "friday": [], "saturday": [], "sunday": []
                                    },
                                    "hashtags": [],
                                    "links": [],
                                    "downloaded": []
                                }
                                with open(profile_path, "w", encoding="utf-8") as fw:
                                    json.dump(data, fw, indent=4, ensure_ascii=False)
                        username = data.get("username", "").strip()
                        if not username:
                            continue

                        # Mevcut profiller listesine ekle
                        profile_key = f"{platform}_{username}"
                        mevcut_profiller.add(profile_key)

                        links = data.get("links", [])
                        downloaded_items = data.get("downloaded", [])
                        downloaded_urls = {item["url"] for item in downloaded_items if
                                           isinstance(item, dict) and "url" in item}

                        # Link sayısını hesapla: downloaded içindeki öğe sayısı
                        # Bu, henüz paylaşılmamış indirilen içeriklerin sayısıdır
                        link_count = len(downloaded_items)

                        # İstatistik sistemine de link sayısını yaz
                        profile_key = f"{platform}_{username}"
                        if profile_key in stats_system.stats_data:
                            stats_system.stats_data[profile_key]["link_count"] = link_count
                        all_profiles.append({
                            "platform": platform,
                            "username": username,
                            "link_count": link_count
                        })
                    except Exception as e:
                        logging.warning(f"JSON okuma hatası: {file} - {str(e)}")

        # stats_system içindeki verileri temizle
        silinecek_istatistikler = set()
        for profile_key in list(stats_system.stats_data.keys()):
            if profile_key not in mevcut_profiller:
                silinecek_istatistikler.add(profile_key)

        for profile_key in list(stats_system.stats_display_queue):
            if profile_key not in mevcut_profiller:
                stats_system.stats_display_queue.remove(profile_key)
                silinecek_istatistikler.add(profile_key)

        for profile_key in silinecek_istatistikler:
            if profile_key in stats_system.stats_data:
                del stats_system.stats_data[profile_key]
            if profile_key in stats_system.last_stats_update:
                del stats_system.last_stats_update[profile_key]
            if profile_key == stats_system.current_displayed_profile:
                stats_system.current_displayed_profile = next(iter(stats_system.stats_display_queue), None)
            logging.info(f"HTML güncelleme sırasında var olmayan profile ait istatistik temizlendi: {profile_key}")

        # Eğer silinen istatistik varsa dosyayı güncelle
        if silinecek_istatistikler:
            stats_system.istatistikleri_kaydet()

        if not all_profiles:
            return '''
            <div class="skeleton-container" id="extra-skeleton">
                <div class="skeleton" style="height: 20px; width: 70%;"></div>
                <div class="skeleton" style="height: 14px; width: 100%;"></div>
                <div class="skeleton" style="height: 14px; width: 90%;"></div>
                <div class="skeleton" style="height: 14px; width: 80%;"></div>
            </div>
            '''
        all_profiles.sort(key=lambda x: x["link_count"])
        html_blocks = []
        for prof in all_profiles:
            link_count = int(prof["link_count"])
            percent = min(100, link_count)
            if link_count == 0:
                percent = 100
                color = "#FF0000"
            elif percent > 80:
                color = "#4CAF50"
            elif percent > 60:
                color = "#8BC34A"
            elif percent > 40:
                color = "#FFC107"
            elif percent > 20:
                color = "#FF9800"
            else:
                color = "#F44336"
            logo_file = "x.png" if prof["platform"] == "twitter" else "instagram.png"
            full_img_path = PROJECT_ROOT / logo_file
            try:
                with open(full_img_path, "rb") as img_f:
                    b64 = base64.b64encode(img_f.read()).decode()
                img_src = f"data:image/png;base64,{b64}"
            except Exception as e:
                logging.warning(f"Logo yüklenemedi: {logo_file} - {str(e)}")
                img_src = ""
            html_blocks.append(f"""
                <div class=\"bar-entry\" style=\"margin-bottom:12px; transition: all 0.4s ease-in-out;\">
                    <div style=\"display:flex; align-items:center; gap:10px; margin-bottom:6px;\">
                        <img src=\"{img_src}\" style=\"height:32px; width:auto;\" />
                        <span style=\"font-size:16px; font-weight:bold; color:#FFFFFF; letter-spacing:0.6px; margin-left:-1px;\">{prof["username"]}</span>
                    </div>
                    <div style=\"background:#333; border-radius:8px; height:14px; width:100%; overflow:hidden;\">
                        <div style=\"background:{color}; width:{percent}%; height:100%; border-radius:8px; transition:width 0.4s ease;\"></div>
                    </div>
                    <div style=\"font-size:13px; font-weight:bold; letter-spacing:1px; color:#FFFFFF; text-align:right; padding-right:4px;\">
                        {prof["link_count"]} link
                    </div>
                </div>
            """)
        full_html = f'''
            <div class="bar-container" style="display:flex; flex-direction:column; gap:10px; transition: all 0.4s ease-in-out;">
                {''.join(html_blocks)}
            </div>
        '''
        return full_html
    except Exception as e:
        logging.error(f"Can barları güncellenirken hata: {str(e)}")
        return ""


def initialize_stats_system():
    global stats_system
    if stats_system.initialized:
        return
    try:
        logging.info("İstatistik sistemi başlatılıyor...")
        stats_system.istatistikleri_yukle()

        if stats_system.stats_data:
            # Yüklenen istatistikler varsa, ilk uygun profilin detaylarını göstermek üzere ayarla
            # Öncelik stats_display_queue'deki ilk elemana verilebilir veya stats_data'dan ilk anahtar alınabilir.
            # Şimdilik stats_data'dan ilk anahtarı alıyoruz.

            # stats_data'daki anahtarları sıralı almak tutarlılık sağlar
            ordered_profile_keys = sorted(list(stats_system.stats_data.keys()))
            first_profile_key = next(iter(ordered_profile_keys), None)

            if first_profile_key:
                stats_system.current_displayed_profile = first_profile_key
                # Eğer stats_display_queue kullanılıyorsa ve bu profil listede yoksa,
                # gösterim döngüsüne dahil olması için ekleyelim (en başa ekleyerek hemen gösterilmesini sağlayabiliriz)
                if first_profile_key not in stats_system.stats_display_queue:
                    stats_system.stats_display_queue.appendleft(first_profile_key)
                logging.info(f"Başlangıçta görüntülenecek istatistik profili ayarlandı: {first_profile_key}")
            else:
                # stats_data dolu ama geçerli bir anahtar bulunamadı (normalde olmamalı)
                stats_system.current_displayed_profile = None
        else:
            # Yüklenecek istatistik verisi bulunamadı
            stats_system.current_displayed_profile = None

        stats_system.worker_thread = threading.Thread(target=stats_worker, daemon=True)
        stats_system.worker_thread.start()
        check_profiles()
        # update_stats_display_html() # Bu çağrı doğrudan ana UI'yi güncellemez, QMainWindow içindeki self.update_stats() yapar.
        stats_system.initialized = True
        logging.info("İstatistik sistemi başarıyla başlatıldı.")
    except Exception as e:
        logging.error(f"İstatistik sistemi başlatılırken hata: {e}")
        stats_system.current_displayed_profile = None  # Hata durumunda da gösterilecek profil olmasın
