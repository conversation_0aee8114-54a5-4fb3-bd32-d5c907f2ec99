import json
import logging
import os
import random
import re
import shutil
import time
import urllib.request
import zipfile
from pathlib import Path

import instaloader
import requests
import undetected_chromedriver as uc
import yt_dlp

from config import detect_platform_key, chromium_links
from utils import create_directory, clean_temp_files, sanitize_filename
from stats import update_can_bars_ui

PROJECT_ROOT = Path(__file__).resolve().parent

# ---- Simplified Instagram processing without authentication ----
def get_simple_instaloader():
    """
    Creates a simple anonymous Instaloader instance for public content.
    No authentication required - uses shortcode-only naming.
    """
    import instaloader

    L = instaloader.Instaloader(
        download_video_thumbnails=False,
        download_comments=False,
        save_metadata=False,
        compress_json=False,
        dirname_pattern="temp_instagram_download",
        filename_pattern="{shortcode}",
        max_connection_attempts=3,
    )

    return L




def download_and_extract(url, extract_to):
    os.makedirs(extract_to, exist_ok=True)
    zip_path = os.path.join(extract_to, "temp.zip")
    urllib.request.urlretrieve(url, zip_path)
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(extract_to)
    os.remove(zip_path)


def setup_portable_chromium():
    platform_key = detect_platform_key()
    links = chromium_links[platform_key]

    base_dir = PROJECT_ROOT / "portable_chromium"
    browser_dir = base_dir / "browser"
    driver_dir = base_dir / "driver"

    # Browser için: ZIP açıldığında oluşan dizin ismine dikkat edin.
    if platform_key.startswith("win"):
        expected_browser_folder = "chrome-win"
    elif platform_key in ["mac", "mac_arm"]:
        expected_browser_folder = "chrome-mac"
    elif platform_key == "linux_x64":
        expected_browser_folder = "chrome-linux"
    else:
        raise Exception("Bilinmeyen platform")

    if not (browser_dir / expected_browser_folder).exists():
        download_and_extract(links["browser_zip"], str(browser_dir))

    # Chromedriver için: Eğer klasörde chromedriver yoksa indir.
    if not os.path.exists(str(driver_dir)):
        os.makedirs(str(driver_dir), exist_ok=True)
    if not any("chromedriver" in f for f in os.listdir(str(driver_dir)) if
               os.path.isfile(os.path.join(str(driver_dir), f))):
        download_and_extract(links["driver_zip"], str(driver_dir))

    # Binary yolu belirleme:
    if platform_key.startswith("win"):
        binary_path = str(browser_dir / expected_browser_folder / "chrome.exe")
    elif platform_key in ["mac", "mac_arm"]:
        binary_path = str(browser_dir / expected_browser_folder / "Chromium.app" / "Contents" / "MacOS" / "Chromium")
    elif platform_key == "linux_x64":
        binary_path = str(browser_dir / expected_browser_folder / "chrome")
    else:
        raise Exception("Bilinmeyen platform için binary yolu ayarlanamadı.")

    # Chromedriver yolunu bulma:
    driver_path = None
    for root, dirs, files in os.walk(str(driver_dir)):
        for file in files:
            if "chromedriver" in file:
                driver_path = os.path.join(root, file)
                break
        if driver_path:
            break
    if not driver_path:
        raise Exception("Chromedriver bulunamadı!")

    return binary_path, driver_path


def get_portable_chromium_options():
    binary_path, driver_path = setup_portable_chromium()
    options = uc.ChromeOptions()
    options.binary_location = binary_path
    options.add_argument("--headless")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_argument("--disable-infobars")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-gpu")
    options.add_argument("--disable-extensions")
    options.add_argument("--disable-popup-blocking")
    options.add_argument("--disable-notifications")
    options.add_argument("--lang=en-US")
    options.add_argument("--window-size=1920,1080")
    return options, driver_path


def download_ffmpeg():
    """ffmpeg'i indirir ve kurar"""
    base_dir = Path(__file__).resolve().parent
    videos_dir = base_dir / "videos"
    os.makedirs(videos_dir, exist_ok=True)
    ffmpeg_dir = videos_dir / "ffmpeg"
    ffmpeg_exe = ffmpeg_dir / "ffmpeg.exe"

    if os.path.exists(ffmpeg_exe):
        # PATH'e ekleyelim ki yt-dlp/instaloader bulsun
        os.environ["PATH"] = str(ffmpeg_dir) + os.pathsep + os.environ.get("PATH", "")
        return str(ffmpeg_dir)

    try:
        os.makedirs(ffmpeg_dir, exist_ok=True)
        ffmpeg_url = (
            "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/"
            "ffmpeg-master-latest-win64-gpl.zip"
        )
        logging.info("ffmpeg indiriliyor.")
        response = requests.get(ffmpeg_url, stream=True)
        zip_path = os.path.join(videos_dir, "ffmpeg.zip")
        with open(zip_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        logging.info("ffmpeg paketi açılıyor.")
        temp_extract = os.path.join(videos_dir, "ffmpeg_temp")
        with zipfile.ZipFile(zip_path, "r") as zip_ref:
            zip_ref.extractall(temp_extract)
            ffmpeg_bin = None
            for root, dirs, files in os.walk(temp_extract):
                if "ffmpeg.exe" in files:
                    ffmpeg_bin = os.path.join(root, "ffmpeg.exe")
                    break
            if ffmpeg_bin:
                shutil.copy2(ffmpeg_bin, ffmpeg_exe)
        shutil.rmtree(temp_extract)
        os.remove(zip_path)
        logging.info("ffmpeg kurulumu tamamlandı")
        # PATH'e ekleyelim ki ffmpeg her yerden çağrılabilsin
        os.environ["PATH"] = str(ffmpeg_dir) + os.pathsep + os.environ.get("PATH", "")
        return str(ffmpeg_dir)

    except Exception as e:
        logging.error(f"ffmpeg kurulumu sırasında hata oluştu: {str(e)}")
        return None


def download_instagram_post_simple(url, output_folder="instagramdownloaded"):
    """
    Simplified Instagram post downloader using shortcode-only naming.
    No authentication required - works with public content only.
    Files are named exactly as: {shortcode}.mp4 and {shortcode}.jpg
    """
    import json, os, shutil, logging, instaloader
    from pathlib import Path
    from utils import clean_temp_files
    from urllib.parse import urlparse

    # Extract shortcode from URL
    try:
        parsed_url = urlparse(url)
        path_parts = parsed_url.path.strip('/').split('/')

        if len(path_parts) >= 2 and path_parts[0] in ('p', 'reel', 'tv'):
            shortcode = path_parts[1]
        else:
            logging.error(f"Invalid Instagram URL format: {url}")
            return False, None, None, None, None
    except Exception as e:
        logging.error(f"URL parse error: {e}")
        return False, None, None, None, None

    # Check if file already exists with this shortcode
    os.makedirs(output_folder, exist_ok=True)
    existing_video = os.path.join(output_folder, f"{shortcode}.mp4")
    existing_image = os.path.join(output_folder, f"{shortcode}.jpg")

    if os.path.exists(existing_video):
        logging.info(f"Instagram video already exists, skipping download: {existing_video}")
        return True, shortcode, existing_video, None, ""
    elif os.path.exists(existing_image):
        logging.info(f"Instagram image already exists, skipping download: {existing_image}")
        return True, shortcode, None, existing_image, ""

    temp_folder = "temp_instagram_download"
    clean_temp_files(temp_folder)
    os.makedirs(temp_folder, exist_ok=True)

    # Create simple anonymous Instaloader
    loader = get_simple_instaloader()
    loader.dirname_pattern = temp_folder
    loader.filename_pattern = "{shortcode}"

    try:
        # Get post object
        post = instaloader.Post.from_shortcode(loader.context, shortcode)

        # Download post
        loader.download_post(post, target="")

        # Get caption
        raw_caption = post.caption if post.caption else ""

        # Find downloaded files and move them with shortcode naming
        video_path = None
        thumbnail_path = None

        for file in os.listdir(temp_folder):
            if file.lower().endswith((".mp4", ".webm")):
                # Video file - rename to {shortcode}.mp4
                video_path = os.path.join(output_folder, f"{shortcode}.mp4")
                shutil.move(os.path.join(temp_folder, file), video_path)
                logging.info(f"Instagram video downloaded: {video_path}")

                # Create thumbnail for video
                from utils import create_video_thumbnail
                thumbnail_path = create_video_thumbnail(video_path)
                if thumbnail_path:
                    # Rename thumbnail to {shortcode}.jpg
                    new_thumbnail_path = os.path.join(output_folder, f"{shortcode}.jpg")
                    if thumbnail_path != new_thumbnail_path:
                        shutil.move(thumbnail_path, new_thumbnail_path)
                        thumbnail_path = new_thumbnail_path
                    logging.info(f"Instagram thumbnail created: {thumbnail_path}")

            elif file.lower().endswith((".jpg", ".jpeg", ".png")):
                # Image file - rename to {shortcode}.jpg
                thumbnail_path = os.path.join(output_folder, f"{shortcode}.jpg")
                shutil.move(os.path.join(temp_folder, file), thumbnail_path)
                logging.info(f"Instagram image downloaded: {thumbnail_path}")

        clean_temp_files(temp_folder)

        if video_path or thumbnail_path:
            return True, shortcode, video_path, thumbnail_path, raw_caption
        else:
            logging.warning(f"No media files found: {url}")
            return False, None, None, None, None

    except instaloader.exceptions.QueryReturnedNotFoundException:
        logging.error(f"Instagram post not found or private: {url}")
        clean_temp_files(temp_folder)
        return False, None, None, None, None
    except instaloader.exceptions.LoginRequiredException:
        logging.error(f"This Instagram post is private, login required: {url}")
        clean_temp_files(temp_folder)
        return False, None, None, None, None
    except Exception as e:
        logging.error(f"Instagram download error: {e}")
        clean_temp_files(temp_folder)
        return False, None, None, None, None


def get_caption_and_shortcode(post):
    """Instagram gönderisinin başlığını veya shortcode'unu alır"""
    try:
        caption = post.caption if post.caption else ""
        caption = caption.encode("ascii", "ignore").decode("ascii")
        caption = caption[:100]
        safe_caption = sanitize_filename(caption)
        if not safe_caption:
            return post.shortcode
        return safe_caption
    except Exception as e:
        logging.error(f"Başlık alınırken hata oluştu: {str(e)}")
        return post.shortcode


def extract_shortcode_from_url(url):
    """
    URL'den shortcode'u çıkarır.
    Eğer URL '/status/' içeriyorsa, bu string'den sonraki kısmı döndürür;
    aksi halde, URL'nin son kısmını döndürür.
    """
    try:
        url = url.strip().split("?")[0].rstrip("/")
        if "/status/" in url:
            return url.split("/status/")[-1]
        else:
            return url.split("/")[-1]
    except Exception as e:
        logging.error(f"URL'den shortcode çıkarılırken hata oluştu: {url} - Hata: {str(e)}")
        return None


# Alias for compatibility with existing code
get_shortcode_from_url = extract_shortcode_from_url


def move_video_to_destination(temp_dir, output_dir, filename):
    """İndirilen videoyu hedef klasöre taşır ve yeni yolu döner"""
    try:
        video_files = [f for f in os.listdir(temp_dir) if f.endswith((".mp4", ".webm"))]
        if video_files:
            old_path = Path(temp_dir) / video_files[0]
            new_path = Path(output_dir) / f"{filename}.mp4"
            counter = 1
            base_path = new_path.with_suffix('')
            while new_path.exists():
                new_path = Path(f"{base_path}-{counter}.mp4")
                counter += 1
            shutil.move(old_path, new_path)
            logging.info(f"Video başarıyla taşındı: {new_path}")
            return True, str(new_path)
    except Exception as e:
        logging.error(f"Video taşınırken hata oluştu: {str(e)}")
    return False, None


def download_instagram_video_simple(shortcode, output_dir):
    """
    Simplified Instagram video download function using shortcode-only naming.
    No authentication required - works with public content only.
    Files are named exactly as: {shortcode}.mp4 and {shortcode}.jpg
    """
    import shutil, os, logging, instaloader
    from utils import clean_temp_files
    from pathlib import Path

    temp_dir = "temp_instagram_download"
    # Reset temp folder
    clean_temp_files(temp_dir)
    os.makedirs(temp_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)

    try:
        # Create simple anonymous Instaloader
        L = get_simple_instaloader()
        L.dirname_pattern = temp_dir
        L.filename_pattern = "{shortcode}"

        # Get post object
        post = instaloader.Post.from_shortcode(L.context, shortcode)

        # Get caption
        raw_caption = post.caption if post.caption else ""

        # Download post
        L.download_post(post, target="")

        # Find downloaded files and move them with shortcode naming
        video_path = None
        thumbnail_path = None

        for file in os.listdir(temp_dir):
            if file.lower().endswith((".mp4", ".webm")):
                # Video file - rename to {shortcode}.mp4
                video_path = os.path.join(output_dir, f"{shortcode}.mp4")
                shutil.move(os.path.join(temp_dir, file), video_path)
                logging.info(f"Instagram video downloaded: {video_path}")

                # Create thumbnail for video
                from utils import create_video_thumbnail
                thumbnail_path = create_video_thumbnail(video_path)
                if thumbnail_path:
                    # Rename thumbnail to {shortcode}.jpg
                    new_thumbnail_path = os.path.join(output_dir, f"{shortcode}.jpg")
                    if thumbnail_path != new_thumbnail_path:
                        shutil.move(thumbnail_path, new_thumbnail_path)
                        thumbnail_path = new_thumbnail_path
                    logging.info(f"Instagram thumbnail created: {thumbnail_path}")

            elif file.lower().endswith((".jpg", ".jpeg", ".png")):
                # Image file - rename to {shortcode}.jpg
                thumbnail_path = os.path.join(output_dir, f"{shortcode}.jpg")
                shutil.move(os.path.join(temp_dir, file), thumbnail_path)
                logging.info(f"Instagram image downloaded: {thumbnail_path}")

        clean_temp_files(temp_dir)

        if video_path or thumbnail_path:
            return True, shortcode, video_path, thumbnail_path, raw_caption
        else:
            logging.error(f"No media files found: {shortcode}")
            return False, None, None, None, None

    except instaloader.exceptions.QueryReturnedNotFoundException:
        logging.error(f"Instagram post not found or private: {shortcode}")
        clean_temp_files(temp_dir)
        return False, None, None, None, None
    except instaloader.exceptions.LoginRequiredException:
        logging.error(f"This Instagram post is private, login required: {shortcode}")
        clean_temp_files(temp_dir)
        return False, None, None, None, None
    except Exception as e:
        logging.error(f"Instagram download error: {e}")
        clean_temp_files(temp_dir)
        return False, None, None, None, None


def is_shorts_url(url):
    """URL'nin YouTube Shorts olup olmadığını kontrol eder"""
    return "/shorts/" in url.lower()


def convert_shorts_to_normal_url(url):
    """Shorts URL'sini normal YouTube URL'sine çevirir"""
    if is_shorts_url(url):
        video_id = url.split("/shorts/")[1].split("?")[0]
        return f"https://www.youtube.com/watch?v={video_id}"
    return url


def download_youtube_video(url, output_dir, ffmpeg_dir):
    """
    YouTube videosunu indirir (MP4 formatında).
    """
    try:
        normalized_url = convert_shorts_to_normal_url(url)
        ydl_opts = {
            "format": "(bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4])/(bestvideo+bestaudio/best)",
            "outtmpl": str(Path(output_dir) / "%(title)s.%(ext)s"),
            "restrictfilenames": True,
            "noplaylist": True,
            "nocheckcertificate": True,
            "ignoreerrors": False,
            "no_warnings": True,
            "quiet": True,
            "ffmpeg_location": ffmpeg_dir,
            "merge_output_format": "mp4",
            "recodevideo": "mp4",
            "ffmpeg_args": ["-crf", "18", "-preset", "fast"],
        }
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(normalized_url, download=False)
            video_title = info.get("title", None)
            if video_title:
                safe_title = sanitize_filename(video_title)
                if safe_title:
                    ydl.download([normalized_url])
                    video_type = "Shorts" if is_shorts_url(url) else "Video"
                    logging.info(f"YouTube {video_type} başarıyla indirildi: {safe_title}")
                    return True
            logging.error(f"Video başlığı alınamadı: {url}")
            return False
    except Exception as e:
        logging.error(f"YouTube videosu indirilirken hata oluştu ({url}): {str(e)}")
        return False


def normalize_url(url):
    """Normalize URL for duplicate detection"""
    if not url:
        return ""
    # Remove query parameters and fragments, normalize domain
    normalized = url.split("?")[0].split("#")[0].strip()
    if "x.com/" in normalized:
        normalized = normalized.replace("x.com", "twitter.com")
    return normalized


def get_twitter_id_from_url(url):
    """Extract Twitter post ID from URL for consistent naming"""
    try:
        # Extract tweet ID from URL patterns like:
        # https://twitter.com/user/status/1234567890
        # https://x.com/user/status/1234567890
        import re
        match = re.search(r'/status/(\d+)', url)
        if match:
            return match.group(1)
        return None
    except:
        return None


def download_twitter_media(url, output_dir, ffmpeg_dir, for_instagram_profile=False):
    """
    Twitter bağlantısından video veya görsel indirir.
    Başarı durumunda: (success: bool, caption: str, file_path: str, original_caption: str)
    Hata durumunda: (False, None, None, None)

    Parameters:
        url: Twitter URL
        output_dir: Output directory
        ffmpeg_dir: FFmpeg directory
        for_instagram_profile: Whether this download is for an Instagram profile (requires thumbnail)
    """
    try:
        if not url or not isinstance(url, str):
            logging.error(f"Geçersiz Twitter URL'si: {url}")
            return False, None, None, None

        # Normalize URL
        normalized_url = normalize_url(url)
        if "x.com/" in url:
            url = url.replace("x.com", "twitter.com")

        os.makedirs(output_dir, exist_ok=True)

        # Extract Twitter ID for consistent naming
        twitter_id = get_twitter_id_from_url(url)

        # Check if file already exists with this Twitter ID
        if twitter_id:
            existing_video = os.path.join(output_dir, f"{twitter_id}.mp4")
            existing_image = os.path.join(output_dir, f"{twitter_id}.jpg")

            if os.path.exists(existing_video):
                logging.info(f"Twitter video already exists, skipping download: {existing_video}")
                return True, twitter_id, existing_video, ""
            elif os.path.exists(existing_image):
                logging.info(f"Twitter image already exists, skipping download: {existing_image}")
                return True, twitter_id, existing_image, ""

        # Bilgi çekme işlemi
        max_retries = 3
        retry_count = 0
        info = None

        while retry_count < max_retries:
            try:
                with yt_dlp.YoutubeDL({"quiet": True}) as ydl:
                    info = ydl.extract_info(url, download=False)
                    if info:
                        break
            except Exception as e:
                logging.warning(f"Twitter bilgisi alınırken hata ({retry_count+1}/{max_retries}): {e}")

            retry_count += 1
            time.sleep(2)  # Yeniden denemeden önce bekle

        if not info:
            logging.error(f"Twitter medya bilgisi alınamadı: {url}")
            return False, None, None, None

        # Tweet açıklamasını tam olarak almak için description alanını kullan (yoksa title)
        raw_caption = (info.get("description") or info.get("title", "")).strip()
        # Tüm linkleri kaldır
        description = re.sub(r'https?://\S+', '', raw_caption).strip()
        original_caption = description

        # Use Twitter ID for filename if available, otherwise use sanitized caption
        if twitter_id:
            safe_title = twitter_id
        else:
            # Caption (dosya adı için) temizlik
            cleaned_caption = re.sub(r'#\w+', '', original_caption).strip()
            safe_title = sanitize_filename(cleaned_caption)
            if not safe_title:
                safe_title = "twitter_" + str(int(time.time()))
                logging.warning(f"Tweet başlığı alınamadı, otomatik başlık oluşturuldu: {safe_title}")

        is_video = any(f.get('vcodec') != 'none' for f in info.get('formats', []))
        is_image = info.get('ext') in ['jpg', 'png'] or (info.get('_type') == 'entry_list' and any(
            e.get('ext') in ['jpg', 'png'] for e in info.get('entries', [])))

        ydl_opts = {
            "outtmpl": str(Path(output_dir) / f"{safe_title}.%(ext)s"),
            "restrictfilenames": True,
            "quiet": True,
            "writeinfojson": False,  # JSON dosyası oluşturma
            "writedescription": False,  # Açıklama dosyası oluşturma
            "writesubtitles": False,  # Altyazı dosyası oluşturma
            "writeautomaticsub": False,  # Otomatik altyazı dosyası oluşturma
        }

        if is_video:
            ydl_opts["format"] = "(bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4])"
            ydl_opts["ffmpeg_location"] = ffmpeg_dir
            ydl_opts["merge_output_format"] = "mp4"
        elif is_image:
            ydl_opts["format"] = "best"
        else:
            logging.warning(f"Desteklenmeyen medya türü: {url}")
            return False, None, None, None

        # İndirme işlemi
        retry_count = 0
        download_success = False

        while retry_count < max_retries and not download_success:
            try:
                with yt_dlp.YoutubeDL(ydl_opts) as ydl2:
                    ydl2.download([url])
                download_success = True
            except Exception as e:
                retry_count += 1
                if retry_count >= max_retries:
                    logging.error(f"Twitter medyası indirilemedi (maksimum deneme sayısına ulaşıldı): {url} - {e}")
                    return False, None, None, None
                logging.warning(f"Twitter medyası indirilirken hata ({retry_count}/{max_retries}): {e}")
                time.sleep(3)  # Yeniden denemeden önce bekle

        expected_ext = ['.mp4'] if is_video else ['.jpg', '.png']
        downloaded_files = [f for f in os.listdir(output_dir)
                            if f.startswith(safe_title)
                            and os.path.splitext(f)[1].lower() in expected_ext]

        if downloaded_files:
            file_path = Path(output_dir) / downloaded_files[0]
            logging.info(f"Twitter medyası indirildi: {file_path}")

            # Instagram profili için indirildiyse ve video ise thumbnail oluştur
            if for_instagram_profile and is_video and file_path.suffix.lower() == '.mp4':
                from utils import create_video_thumbnail
                thumbnail_path = create_video_thumbnail(str(file_path))
                logging.info(f"Instagram profili için Twitter video thumbnail'i oluşturuldu: {thumbnail_path}")

            return True, safe_title, str(file_path), original_caption
        else:
            logging.error(f"İndirilen dosya bulunamadı: {url}")
            return False, None, None, None

    except Exception as e:
        logging.error(f"Twitter indirme hatası: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return False, None, None, None


def process_instagram_downloads_from_profiles(instagram_output_dir, temp_dir):
    """
    Instagram için 'configuration/instagram' klasöründeki tüm profil .json dosyalarındaki
    linkleri indirir. Hem Instagram hem Twitter linklerini işler ve indirilen her medya
    dosyasının yolunu ve açıklamasını ilgili profil JSON'una kaydeder.

    Yeni yaklaşım: Instagram linkleri için kimlik doğrulama gerektirmez, sadece shortcode
    tabanlı dosya adlandırma kullanır. Dosyalar {shortcode}.mp4 ve {shortcode}.jpg olarak adlandırılır.
    """
    base_dir = Path(__file__).resolve().parent
    config_dir = base_dir / "configuration" / "instagram"
    ffmpeg_dir = download_ffmpeg()

    if not config_dir.exists():
        logging.info(f"Instagram konfigürasyon klasörü bulunamadı: {config_dir}")
        return

    profile_files = [f for f in os.listdir(config_dir) if f.endswith(".json")]
    logging.info(f"Toplam {len(profile_files)} Instagram profili bulundu.")

    for pfile in profile_files:
        profile_path = config_dir / pfile
        try:
            with open(profile_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            links = data.get("links", [])
            downloaded_urls = {
                normalize_url(item["url"])
                for item in data.get("downloaded", [])
                if isinstance(item, dict) and "url" in item
            }
            links = [link for link in links if normalize_url(link) not in downloaded_urls]
            if not links:
                logging.info(f"Bu profilde indirilecek yeni link yok: {pfile}")
                continue

            # İndirilenleri kaydedeceğimiz liste
            downloaded = data.get("downloaded", [])

            logging.info(f"--- Profil: {pfile}, Link sayısı: {len(links)} ---")
            for i, url in enumerate(links, 1):
                try:
                    logging.info(f"İndiriliyor (Instagram Profili İçinde): {url} ({i}/{len(links)})")
                    success_download = False
                    shortcode = None
                    video_path = None
                    thumbnail_path = None
                    original_caption = None

                    if "twitter.com" in url or "x.com" in url:
                        success_download, caption, file_path, original_caption = download_twitter_media(
                            url, instagram_output_dir, ffmpeg_dir, for_instagram_profile=True
                        )
                        # For Twitter content, use the caption as shortcode for consistency
                        shortcode = caption
                        video_path = file_path
                    else:
                        # Instagram link - use new simplified approach
                        success_download, shortcode, video_path, thumbnail_path, original_caption = download_instagram_post_simple(
                            url, instagram_output_dir
                        )

                    if success_download and shortcode:
                        logging.info(f"✓ Medya indirildi: {video_path or thumbnail_path}")

                        # Create download entry with shortcode
                        download_entry = {
                            "url": url,
                            "shortcode": shortcode,
                            "original_caption": original_caption
                        }

                        # Add file paths
                        if video_path:
                            download_entry["video_path"] = video_path
                        if thumbnail_path:
                            download_entry["thumbnail_path"] = thumbnail_path

                        # Legacy compatibility - keep file_path for main media
                        download_entry["file_path"] = video_path or thumbnail_path
                        download_entry["caption"] = shortcode

                        downloaded.append(download_entry)
                        data["downloaded"] = downloaded

                        # Dosyayı güvenli bir şekilde kaydet
                        try:
                            with open(profile_path, "w", encoding="utf-8") as f:
                                json.dump(data, f, indent=4, ensure_ascii=False)

                            # Can barlarını anlık güncelle
                            update_can_bars_ui()
                        except Exception as save_error:
                            logging.error(f"Profil JSON kaydedilirken hata: {save_error}")
                    else:
                        logging.error(f"Medya indirilemedi ({i}/{len(links)}): {url}")

                except Exception as e:
                    logging.error(f"Link işlenirken hata ({i}/{len(links)}): {url} -> {e}")
                    import traceback
                    logging.error(traceback.format_exc())

                # İndirme işlemleri arasında rastgele bekle
                if i < len(links):
                    delay = random.uniform(5, 12)
                    logging.info(f"Sonraki indirme için {delay:.2f} saniye bekleniyor...")
                    time.sleep(delay)

        except Exception as e:
            logging.error(f"Profil dosyası okunurken hata oluştu ({pfile}): {e}")
            import traceback
            logging.error(traceback.format_exc())


def download_all_profiles_links():
    """
    Tüm platformlardaki tüm profillerin .json içindeki 'links' listesini indirir.
    """
    base_dir = Path(__file__).resolve().parent
    videos_dir = base_dir / "videos"
    temp_dir = videos_dir / "temp"
    ffmpeg_dir = download_ffmpeg()

    # Çıktı klasörleri
    instagram_output_dir = videos_dir / "instagramdownloaded"
    twitter_output_dir = videos_dir / "twitterdownloaded"
    youtube_output_dir = videos_dir / "youtubedownloaded"

    create_directory(str(instagram_output_dir))
    create_directory(str(twitter_output_dir))
    create_directory(str(youtube_output_dir))
    create_directory(str(temp_dir))

    # Her platform için "downloads_from_profiles" fonksiyonunu çağır
    # Instagram indirme işlemi artık tek bir oturum kullanıyor
    logging.info("Instagram profil linklerini indirme işlemi başlatılıyor...")
    process_instagram_downloads_from_profiles(str(instagram_output_dir), str(temp_dir))

    logging.info("Twitter profil linklerini indirme işlemi başlatılıyor...")
    process_twitter_downloads_from_profiles(str(twitter_output_dir), ffmpeg_dir)

    logging.info("YouTube profil linklerini indirme işlemi başlatılıyor...")
    process_youtube_downloads_from_profiles(str(youtube_output_dir), ffmpeg_dir)

    logging.info("Tüm platformların link indirme işlemleri tamamlandı.")


def process_youtube_downloads_from_profiles(youtube_output_dir, ffmpeg_dir):
    """
    YouTube için 'configuration/youtube' klasöründeki tüm profil .json dosyalarını okur.
    Her profilin "links" listesindeki her bir linki indirir.
    """
    base_dir = Path(__file__).resolve().parent
    config_dir = base_dir / "configuration" / "youtube"

    if not config_dir.exists():
        logging.info(f"YouTube konfigürasyon klasörü bulunamadı: {config_dir}")
        return

    profile_files = [f for f in os.listdir(config_dir) if f.endswith(".json")]
    total_profiles = len(profile_files)
    logging.info(f"Toplam {total_profiles} YouTube profili bulundu.")

    for pfile in profile_files:
        profile_path = config_dir / pfile
        try:
            with open(profile_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            links = data.get("links", [])
            downloaded_urls = {normalize_url(item["url"]) for item in data.get("downloaded", []) if
                               isinstance(item, dict) and "url" in item}
            links = [link for link in links if normalize_url(link) not in downloaded_urls]
            if not links:
                logging.info(f"Bu profilde indirilecek yeni link yok: {pfile}")
                continue
            logging.info(f"--- Profil: {pfile}, Link sayısı: {len(links)} ---")
            for i, url in enumerate(links, 1):
                logging.info(f"İndiriliyor (YouTube): {url} ({i}/{len(links)})")
                success = download_youtube_video(url, youtube_output_dir, ffmpeg_dir)
                if success:
                    video_type = "Shorts" if is_shorts_url(url) else "Video"
                    logging.info(f"YouTube {video_type} indirildi ({i}/{len(links)})")
                else:
                    logging.error(f"YouTube video indirilemedi ({i}/{len(links)})")
                time.sleep(1)
        except Exception as e:
            logging.error(f"Profil dosyası okunurken hata oluştu: {pfile} -> {str(e)}")


def process_twitter_downloads_from_profiles(twitter_output_dir, ffmpeg_dir):
    """
    Twitter için 'configuration/twitter' klasöründeki tüm profil .json dosyalarını okur.
    Her profilin "links" listesindeki her bir linki (Twitter veya Instagram) indirir.
    """
    base_dir = Path(__file__).resolve().parent
    config_dir = base_dir / "configuration" / "twitter"
    videos_dir = base_dir / "videos"
    # Geçici dosyalar için kullanılacak dizin (varsayılan temp_dir veya benzeri)
    temp_dir = videos_dir / "temp"

    # Instagram session directory for handling Instagram links in Twitter profiles
    instagram_config_dir = base_dir / "configuration" / "instagram"
    instagram_session_dir = instagram_config_dir / "sessions"
    os.makedirs(str(instagram_session_dir), exist_ok=True)

    # No longer need Instagram credentials - using simplified anonymous approach

    # Gerekli dizinlerin var olduğundan emin ol
    if not config_dir.exists():
        logging.info(f"Twitter konfigürasyon klasörü bulunamadı: {config_dir}")
        return

    create_directory(str(videos_dir))  # videos dizinini oluştur (eğer yoksa)
    create_directory(str(temp_dir))  # temp dizinini oluştur (eğer yoksa)

    profile_files = [f for f in os.listdir(config_dir) if f.endswith(".json")]
    total_profiles = len(profile_files)
    logging.info(f"Toplam {total_profiles} Twitter profili bulundu.")

    for pfile in profile_files:
        profile_path = config_dir / pfile
        try:
            with open(profile_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            # İndirilenler listesini güvenli bir şekilde al ve sadece sözlük olanları filtrele
            downloaded = [item for item in data.get("downloaded", []) if isinstance(item, dict)]

            # İndirilmiş URL'lerin setini oluştur (normalize edilmiş)
            downloaded_urls = {normalize_url(item.get("url")) for item in downloaded if item.get("url")}

            # Henüz indirilmemiş linkleri belirle (normalize edilmiş URL'lerle karşılaştır)
            links_to_process = [link for link in data.get("links", []) if normalize_url(link) not in downloaded_urls]

            if not links_to_process:
                logging.info(f"Bu profilde indirilecek yeni link yok: {pfile}")
                continue

            logging.info(f"--- Profil: {pfile}, İndirilecek Link sayısı: {len(links_to_process)} ---")

            # İndirilecek her linki işle
            for i, url in enumerate(links_to_process, 1):
                logging.info(f"İndiriliyor (Twitter Profili İçinde): {url} ({i}/{len(links_to_process)})")

                success_download = False
                caption = None
                file_path = None
                original_caption = None  # Instagram için orijinal başlığı tutar

                if "instagram.com" in url:
                    # Instagram link - use new simplified approach with shortcode naming
                    success_download, shortcode, video_path, thumbnail_path, original_caption = download_instagram_post_simple(
                        url, str(twitter_output_dir)
                    )
                    # For compatibility with existing code, set caption and file_path
                    caption = shortcode
                    file_path = video_path or thumbnail_path

                elif "twitter.com" in url or "x.com" in url:
                    # Twitter linkini indirmek için güncellenmiş download_twitter_media fonksiyonunu kullan
                    # download_twitter_media fonksiyonunun (success, caption, file_path) döndürdüğünü varsayıyoruz
                    success_download, caption, file_path, original_caption = download_twitter_media(url, str(twitter_output_dir), ffmpeg_dir)
                    # Twitter indirmeleri için original_caption None kalacaktır.

                # İndirme başarılı olduysa ve dosya yolu ile başlık varsa
                if success_download and file_path and caption:
                    logging.info(f"Medya başarıyla indirildi: {file_path}")
                    # İndirilen öğeyi downloaded listesine ekle
                    downloaded.append({
                        "url": url,
                        "file_path": file_path,
                        "caption": caption,  # Kullanılacak güvenli başlık
                        "original_caption": original_caption  # Instagram için varsa orijinal başlık
                    })

                    # Güncellenmiş downloaded listesini data içine kaydet
                    data["downloaded"] = downloaded

                    # Güncellenmiş profil verisini JSON dosyasına geri yaz
                    with open(profile_path, "w", encoding="utf-8") as f:
                        json.dump(data, f, indent=4, ensure_ascii=False)

                    # Can barlarını anlık güncelle
                    update_can_bars_ui()

                else:
                    logging.error(f"Medya indirilemedi ({i}/{len(links_to_process)}): {url} - Açık bir gönderi değilse veya Instagram oturumu gerekiyorsa, geçerli bir Instagram hesabı ekleyin.")

                # İstekler arasında bekleme ekle (oran sınırlamalarını aşmamak için)
                time.sleep(random.uniform(5, 10))  # Örnek bekleme süresi


        except Exception as e:
            logging.error(f"Profil dosyası işlenirken hata oluştu: {pfile} -> {str(e)}")
